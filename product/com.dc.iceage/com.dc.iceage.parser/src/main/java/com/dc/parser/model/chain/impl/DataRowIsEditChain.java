package com.dc.parser.model.chain.impl;

import com.dc.parser.constants.DCConstants;
import com.dc.parser.model.chain.SqlCheckChain;
import com.dc.parser.service.MetaDataStoreService;
import com.dc.parser.type.SensitiveDataType;
import com.dc.repository.mysql.mapper.SecurityRuleDetailsMapper;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.SqlFieldDataDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import com.dc.sqlparser.types.EDbObjectType;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.sql.model.TableColumnModel;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.summer.parser.utils.CustomSqlStatementUtils;
import com.dc.summer.parser.utils.DatabaseTypeUtils;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.DatabaseType;
import com.dc.type.SceneType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DataRowIsEditChain extends SqlCheckChain {

    private final MetaDataStoreService metaDataStoreService = Resource.getBeanRequireNonNull(MetaDataStoreService.class);

    public DataRowIsEditChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel) {
        super(parserParamDto, sqlParseModel);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        try {

            final Integer dbType = parserParamDto.getDbType();
            final DatabaseType databaseType = DatabaseType.of(dbType);

            List<DataMask> dataMasklist = webSQLParserResult.getDataMask();

            if (SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlParseModel.getOperation())) {

                // gBase8a/clickhouse/hive/inceptor/spark/oscar_cluster 主键值可重复,当作无主键处理
                // impala(Impala 2.5.0-cdh5.7.0)/hetu 无主键
                if (Arrays.asList(DatabaseType.G_BASE_8A.getValue(), DatabaseType.CLICKHOUSE.getValue(), DatabaseType.HIVE.getValue(),
                        DatabaseType.ADBMYSQL2.getValue(), DatabaseType.IMPALA.getValue(), DatabaseType.INCEPTOR.getValue(),
                        DatabaseType.HETU.getValue(), DatabaseType.SPARK.getValue(), DatabaseType.OSCAR_CLUSTER.getValue()).contains(dbType)) {
                    webSQLParserResult.setScene(SceneType.NO_PRIMARY_KEY.getValue());
                    return true;
                } else if (DatabaseType.MONGODB.getValue().equals(dbType)) {
                    if (sqlParseModel.getSqlAuthModelList().size() > 0) {
                        webSQLParserResult.setTableName(sqlParseModel.getSqlAuthModelList().get(0).getName());
                        if (sqlParseModel.getSqlAuthModelList().get(0).isTable() && sqlParseModel.getCustomSqlStatement() != null
                                && Arrays.asList(SqlConstant.MONGO_CAN_EDIT).contains(sqlParseModel.getCustomSqlStatement().getOperation())) {
                            webSQLParserResult.setReadonly(false);
                        } else {
                            webSQLParserResult.setScene(SceneType.VIEW.getValue());
                        }
                    }
                    return true;
                } else if (Arrays.asList(DatabaseType.REDIS.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(dbType)) {
                    // redis窗口执行都不可编辑,打开表等由前端判断是否可编辑;es不可编辑
                    webSQLParserResult.setReadonly(true);
                    return true;
                } else if (DatabaseType.H_BASE.getValue().equals(dbType)) {
                    // hBase都可编辑
                    webSQLParserResult.setReadonly(false);
                    webSQLParserResult.setPrimaryKeyColumns(new ArrayList<>(List.of("RowKey", "Family", "Cell")));
                    SqlAuthModel sqlAuthModel = sqlParseModel.getSqlAuthModelList().get(0);
                    String tableName = sqlAuthModel.getFullName();
                    webSQLParserResult.setTableName(tableName);
                    return true;
                }

                int canEdit = CommonUtil.canEdit(sqlParseModel);

                if (Arrays.asList(SceneType.CAN_EDIT.getValue(), SceneType.JOIN_SELF.getValue(), SceneType.FUNCTION.getValue()).contains(canEdit)) {
                    SqlAuthModel sqlAuthModel = sqlParseModel.getSqlAuthModelList().get(0);
                    DCustomSqlStatement tCustomSqlStatement = sqlParseModel.gettCustomSqlStatement();
                    // 获取表名
                    String tableName = sqlAuthModel.getFullName().trim();
                    webSQLParserResult.setTableName(tableName);
                    String schemaName = sqlAuthModel.getSchemaName();
                    // 获取主键
                    List<String> columnPrimaryKeyTemp;
                    List<String> columns = new ArrayList<>();
                    if (DatabaseType.supportRewriteSqlWithRowId().contains(databaseType)) {
                        List<TableColumnModel> tableColumns = metaDataStoreService.getTableColumns(parserParamDto, sqlAuthModel.getName(), sqlAuthModel.getSchemaName(), sqlAuthModel.isTable());
                        columnPrimaryKeyTemp = tableColumns.stream()
                                .filter(tableColumn -> tableColumn.getIsPrimaryKey() != null && tableColumn.getIsPrimaryKey() == 1)
                                .map(TableColumnModel::getColumnName)
                                .collect(Collectors.toList());
                        columns = tableColumns.stream()
                                .map(TableColumnModel::getColumnName)
                                .collect(Collectors.toList());
                    } else {
                        String tableNameTemp = DatabaseType.OSCAR_CLUSTER.getValue().equals(dbType) ? parserParamDto.getTableId() : tableName;
                        columnPrimaryKeyTemp = metaDataStoreService.getColumnPrimaryKey(parserParamDto, tableNameTemp, schemaName);
                        if (DatabaseType.ADBMYSQL3.getValue().equals(dbType)) {
                            columnPrimaryKeyTemp.remove("__adb_auto_id__");
                        }
                    }
                    if (CollectionUtils.isNotEmpty(columnPrimaryKeyTemp)) {
                        webSQLParserResult.setPrimaryKeyColumns(columnPrimaryKeyTemp);

                        // join自身、函数的情况不可编辑，但需要返回主键和表名
                        if (Arrays.asList(SceneType.JOIN_SELF.getValue()).contains(canEdit)) {
                            webSQLParserResult.setScene(canEdit);
                            return true;
                        }

                        List<String> columnPrimaryKey = new ArrayList<>();
                        for (String pk : columnPrimaryKeyTemp) {
                            columnPrimaryKey.add(pk.toUpperCase());
                        }
                        boolean flag = true;
                        // 判断主键脱敏情况
                        if (null != dataMasklist) {
                            for (DataMask dataMask : dataMasklist) {
                                if (columnPrimaryKey.contains(dataMask.getColumnName().toUpperCase()) && !SensitiveDataType.CLEAR.getValue().equals(dataMask.getAuthLevel())) {
                                    webSQLParserResult.setScene(SceneType.PRIMARY_KEY_MASK.getValue());
                                    webSQLParserResult.setPrimaryKeyColumns(null);
                                    flag = false;
                                    break;
                                }
                            }
                        }

                        if (flag) {

                            // 获取与字段名对应的别名
                            Map<String, String> aliasMap = new HashMap<>();
                            if (sqlParseModel.getAction().getAlias() != null) {
                                for (Map.Entry<String, String> entry : sqlParseModel.getAction().getAlias().entrySet()) {
                                    String oldEntryKey = entry.getKey();
                                    String[] split = oldEntryKey.split("\\.");
                                    if (!"*".equals(split[split.length - 1])) {
                                        aliasMap.put(split[split.length - 1], entry.getValue());
                                    }
                                }
                            }

                            // 判断SQL查询是否返回了主键列名
                            if (CollectionUtils.isNotEmpty(sqlParseModel.getAction().getColumns())) {
                                int allPK = 0;
                                boolean isStar = false;
                                for (String column : sqlParseModel.getAction().getColumns()) {
                                    String splitRegex = CommonUtil.useColonSplit(dbType) ? ":|\\." : "\\.";
                                    String[] columnSplit = column.split(splitRegex);
                                    if ("*".equalsIgnoreCase(columnSplit[columnSplit.length - 1])) {
                                        isStar = true;
                                        break;
                                    }
                                    // 主键别名与主键名不一致，也不能编辑
                                    String alias = aliasMap.get(columnSplit[columnSplit.length - 1].toUpperCase());
                                    if (columnPrimaryKey.contains(columnSplit[columnSplit.length - 1].toUpperCase())
                                            && alias != null && alias.equals(columnSplit[columnSplit.length - 1])) {
                                        allPK++;
                                    }
                                }
                                // 联合主键没有被全部查询，不允许编辑
                                if (!isStar && allPK < columnPrimaryKey.size()) {
                                    webSQLParserResult.setScene(SceneType.NO_PRIMARY_KEY.getValue());
                                    webSQLParserResult.setPrimaryKeyColumns(null);
                                    flag = false;
                                }
                            }

                        }

                        if (flag) {
                            webSQLParserResult.setReadonly(false);
                            webSQLParserResult.setHasPrimaryKey(true);
                        }

                    } else {
                        webSQLParserResult.setScene(SceneType.NO_PRIMARY_KEY.getValue());

                        // join自身、函数的情况不可编辑，但需要返回主键和表名，没有主键也需要return，不能走到改写sql
                        if (Arrays.asList(SceneType.JOIN_SELF.getValue(), SceneType.FUNCTION.getValue()).contains(canEdit)) {
                            webSQLParserResult.setScene(canEdit);
                            return true;
                        }
                    }

                    // 修改sql
                    if (DatabaseType.supportRewriteSqlWithRowId().contains(databaseType)) {
                        // 不是table都不需要编辑
                        boolean isTable = sqlAuthModel.isTable();
                        if (!isTable) {
                            webSQLParserResult.setScene(SceneType.VIEW.getValue());
                            webSQLParserResult.setReadonly(true);
                            return true;
                        }

                        if (columns.isEmpty()) {
                            return buildReadOnlyResult(webSQLParserResult);
                        }

                        if (tCustomSqlStatement instanceof TSelectSqlStatement) {
                            TSelectSqlStatement tSelectSqlStatement = (TSelectSqlStatement) tCustomSqlStatement;
                            if (tSelectSqlStatement.getSelectDistinct() != null) {
                                return buildReadOnlyResult(webSQLParserResult);
                            }
                        }
                        //sql语句中是否本来就包括rowId
                        boolean alreadyExistsRowId = alreadyExistsRowId(tCustomSqlStatement);

                        //是否已经人为地、手动地增加了rowId
                        boolean manuallyAddedRowId = false;

                        // 修改sql
                        if (tCustomSqlStatement != null && tCustomSqlStatement.getCteList() != null) {
                            // with as 都不可编辑
                            return buildReadOnlyResult(webSQLParserResult);
                        } else if (tCustomSqlStatement != null && tCustomSqlStatement.getResultColumnList() != null) {
                            if (tCustomSqlStatement.getTables() != null) {
                                TTableList tables = tCustomSqlStatement.getTables();
                                for (TTable table : tables) {
                                    if (table != null && table.getSubquery() != null) {
                                        // 带有subQuery 的不可编辑
                                        return buildReadOnlyResult(webSQLParserResult);
                                    }
                                }
                            }
                            TResultColumnList tResultColumns = tCustomSqlStatement.getResultColumnList();
                            if (tResultColumns != null && tResultColumns.size() > 0) {
                                TResultColumnList newResultColumns = new TResultColumnList();

                                //待定的rowId，列对象
                                TResultColumn rowidResultColumn = null;
                                if (!alreadyExistsRowId) {
                                    SecurityRuleDetailsMapper securityRuleDetailsMapper = Resource.getBeanRequireNonNull(SecurityRuleDetailsMapper.class);
                                    String appendRowId = securityRuleDetailsMapper.getSecurityRuleValue("append_rowid", parserParamDto.getConnectId());

                                    if (appendRowId == null || "1".equals(appendRowId)) {
                                        TResultColumn rowidBorn = new TResultColumn();
                                        rowidBorn.setExpr(DPSqlParser.parseExpression(DatabaseTypeUtils.getEDbVendor(dbType), "rowid"));
                                        rowidResultColumn = rowidBorn;
                                    }
                                }


                                for (TResultColumn tResultColumn : tResultColumns) {
                                    TObjectName columnFullname = tResultColumn.getColumnFullname();
                                    if (columnFullname == null) {
                                        return buildReadOnlyResult(webSQLParserResult);
                                    }
                                    String[] split = columnFullname.toString().split("\\.");
                                    if (split.length == 0) {
                                        return buildReadOnlyResult(webSQLParserResult);
                                    }
                                    if (split[split.length - 1].contains("*")) {
                                        // "*" 不能设定别名
                                        if (tResultColumn.getAliasClause() != null) {
                                            return buildReadOnlyResult(webSQLParserResult);
                                        }
                                        String alias = "";
                                        if (split.length == 2) {
                                            alias = split[split.length - 2];
                                        }
                                        for (String col : columns) {
                                            TResultColumn newResultColumn = new TResultColumn();
                                            if (!alias.isEmpty()) {
                                                col = alias + ".\"" + col + "\"";
                                            } else {
                                                col = "\"" + col + "\"";
                                            }
                                            newResultColumn.setExpr(DPSqlParser.parseExpression(DatabaseTypeUtils.getEDbVendor(dbType), col));
                                            newResultColumns.addResultColumn(newResultColumn);
                                        }
                                    } else {
                                        String s = columnFullname.toString();
                                        // 查询字段不是表的字段，都不可编辑
                                        if (!columns.contains(split[split.length - 1].toUpperCase().replaceAll("\"", ""))
                                                && !"ROWID".equalsIgnoreCase(split[split.length - 1].replaceAll("\"", ""))) {
                                            return buildReadOnlyResult(webSQLParserResult);
                                        }
                                        if ("ROWID".equalsIgnoreCase(split[split.length - 1].replaceAll("\"", "")) && tResultColumns.size() == 1) {
                                            return buildReadOnlyResult(webSQLParserResult);
                                        }
                                        if (s.contains("||") || s.contains("/") || s.contains("*") || s.contains("-") || s.contains("+")) {
                                            return buildReadOnlyResult(webSQLParserResult);
                                        }
                                        newResultColumns.addResultColumn(tResultColumn);
                                    }
                                }

                                if (rowidResultColumn != null) {
                                    newResultColumns.addResultColumn(rowidResultColumn);
                                    manuallyAddedRowId = true;
                                }
                                if (newResultColumns.size() > 1) {
                                    tCustomSqlStatement.setResultColumnList(newResultColumns);
                                }
                            }
                        }


                        if (tCustomSqlStatement != null && manuallyAddedRowId) {
                            try {
                                String expr = "rowid as \"" + DCConstants.SQL_RES_ROW_ID + "\"";
                                String sql = CustomSqlStatementUtils.toScript(tCustomSqlStatement);
                                sql = sql.replaceFirst("rowid", expr);
                                webSQLParserResult.setExecuteSql(sql);
                                webSQLParserResult.setSqlHasChanged(true);
                                webSQLParserResult.setReadonly(false);

                                SqlFieldDataDto sqlFieldData = new SqlFieldDataDto();
                                sqlFieldData.setFieldAlias(DCConstants.SQL_RES_ROW_ID);
                                sqlFieldData.setFieldName("ROWID");
                                sqlFieldData.setCatalogName(sqlAuthModel.getCatalogName());
                                sqlFieldData.setSchemaName(schemaName);
                                sqlFieldData.setTableName(tableName);
                                sqlFieldData.setWithinFunc(false);

                                List<SqlFieldDataDto> sqlFieldDataList = new ArrayList<>();
                                sqlFieldDataList.add(sqlFieldData);
                                sqlFieldDataList.addAll(webSQLParserResult.getSqlFieldDataList());
                                webSQLParserResult.setSqlFieldDataList(sqlFieldDataList);
                            } catch (Exception e) {
                                log.error("改写sql出错！", e);
                            }
                        }

                    }
                } else {
                    webSQLParserResult.setScene(canEdit);
                }

            }

        } catch (Exception e) {
            log.error("是否可编辑，解析异常：", e);
            webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
            webSQLParserResult.setMessage("是否可编辑-解析异常! " + e.getMessage());
            return false;
        } finally {
            // 可编辑和只是因为主键问题导致不可编辑的情况都可生成sql #23802
            if (!webSQLParserResult.isReadonly()
                    || Arrays.asList(SceneType.PRIMARY_KEY_MASK.getValue(), SceneType.NO_PRIMARY_KEY.getValue(), SceneType.FUNCTION.getValue(),
                    // oracle视图需要能生成sql #23956
                    SceneType.JOIN_SELF.getValue(), SceneType.VIEW.getValue()).contains(webSQLParserResult.getScene())) {
                webSQLParserResult.setCanGenerateSql(true);
            }
        }

        return true;
    }

    private static boolean buildReadOnlyResult(WebSQLParserResult webSQLParserResult) {
        webSQLParserResult.setScene(SceneType.READ_ONLY.getValue());
        webSQLParserResult.setReadonly(true);
        return true;
    }

    protected boolean alreadyExistsRowId(DCustomSqlStatement statement) {
        if (statement instanceof TSelectSqlStatement) {
            boolean exists = false;
            TSelectSqlStatement selectStatement = (TSelectSqlStatement) statement;
            for (TResultColumn r : selectStatement.getResultColumnList()) {
                if (r.getExpr() != null && r.getExpr().getObjectOperand() != null) {
                    TObjectName objectOperand = r.getExpr().getObjectOperand();
                    if (objectOperand.getDbObjectType() == EDbObjectType.column && "rowid".equalsIgnoreCase(objectOperand.getPartToken().toString())) {
                        exists = true;
                        break;
                    }
                }
            }
            return exists;
        }
        return false;
    }


}
