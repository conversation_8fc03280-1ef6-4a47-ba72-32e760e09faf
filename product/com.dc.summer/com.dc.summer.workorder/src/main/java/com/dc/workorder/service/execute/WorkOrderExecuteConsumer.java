package com.dc.workorder.service.execute;

import com.dc.annotation.SQL;
import com.dc.config.ApiConfig;
import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.repository.mysql.mapper.OrderDownloadFileMapper;
import com.dc.repository.mysql.model.*;
import com.dc.springboot.core.client.ParserSqlClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.chain.StreamChainRunner;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.log.SensitiveAuthDetail;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.log.SqlRecord;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.recovery.BackupModel;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.springboot.core.model.sensitive.GradedClassifiedModel;
import com.dc.springboot.core.model.type.*;
import com.dc.springboot.core.model.workorder.*;
import com.dc.summer.DBException;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.data.transfer.registry.DataTransferProcessorDescriptor;
import com.dc.summer.data.transfer.registry.DataTransferRegistry;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.model.type.RecordSignType;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.model.chain.impl.*;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.model.type.LineDelimiterType;
import com.dc.summer.model.type.PageSelectedType;
import com.dc.summer.model.type.WebAsyncTaskType;
import com.dc.summer.model.type.WebDataFormat;
import com.dc.summer.model.utils.SqlTrimUtil;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.summer.parser.utils.model.ActionModel;
import com.dc.summer.registry.center.Global;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.WaterMarkService;
import com.dc.summer.service.result.WorkOrderTransferResult;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.service.transfer.WebDataTransfer;
import com.dc.summer.service.transfer.WebDataTransferHelper;
import com.dc.summer.service.transfer.WebDataTransferName;
import com.dc.type.DatabaseType;
import com.dc.utils.Pair;
import com.dc.utils.PairList;
import com.dc.utils.http.FileUtil;
import com.dc.workorder.model.Sensitive;
import com.dc.workorder.model.type.ExecuteCommitType;
import com.dc.workorder.model.type.SqlHistoryType;
import com.dc.repository.mysql.service.OrderSqlParserService;
import com.dc.workorder.service.parser.SqlParser;
import com.dc.workorder.service.parser.SqlParserBuilder;
import com.dc.workorder.utils.IdentityInsertUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.SqlSessionTemplate;

import java.io.File;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class WorkOrderExecuteConsumer {

    private final Gson gson = new GsonBuilder().serializeNulls().create();

    private final Queue<OrderSqlParse> queue;

    private final ExecuteOrderMessage message;

    private final OrderTypeKey orderTypeKey;

    private final OrderApplyContent applyContent;

    private final DBRProgressMonitor monitor;

    private final OrderSqlParserService orderSqlParserService;

    private final WaterMarkService waterMarkService;

    private final SqlParser sqlParser;

    private final SummerConfig config;

    ArrayList<File> files;

    private final String taskIdConsumer;

    private final WebAsyncTaskInfo taskInfoProducer;

    private final WebSQLContextInfo contextInfoConsumer;

    private final ExecuteCommitType executeCommitType;

    private final boolean isActiveInstance;

    private final boolean isActiveSchema;

    private final String userId;

    private final OrderDownloadFileMapper downloadFileMapper;

    private final String scriptName;

    private final int maxSize;

    private boolean needCommit = true;

    private final Order order;

    private final AtomicReference<Boolean> consumerFail;

    private final MessageService messageService = Resource.getBean(MessageService.class);

    private final ParserSqlClient parserSqlClient = Resource.getBean(ParserSqlClient.class);

    private final SqlSessionTemplate sqlSessionTemplate = Resource.getBean(SqlSessionTemplate.class);

    private final Map<String, Boolean> isIncrementField = new HashMap<>();

    private final OrderExecuteResultService executeResultService;

    private final OrderExecuteResult orderExecuteResult;

    private Integer schemaTaskId;

    private Pair<OrderSchemaTask, BatchWorkOrderInstance> pair;

    public WorkOrderExecuteConsumer(Queue<OrderSqlParse> queue,
                                    OrderTypeKey orderTypeKey,
                                    ExecuteOrderMessage message,
                                    DBRProgressMonitor monitor,
                                    OrderSqlParserService orderSqlParserService,
                                    SummerConfig config,
                                    String taskIdConsumer,
                                    WebAsyncTaskInfo taskInfoProducer,
                                    WebSQLContextInfo contextInfoConsumer,
                                    ExecuteCommitType executeCommitType,
                                    boolean isActiveInstance,
                                    boolean isActiveSchema,
                                    String userId,
                                    OrderDownloadFileMapper downloadFileMapper,
                                    String scriptName,
                                    Order order,
                                    int maxSize,
                                    AtomicReference<Boolean> consumerFail,
                                    OrderExecuteResultService executeResultService,
                                    Integer orderExecuteId) {
        this.queue = queue;
        this.message = message;
        this.orderTypeKey = orderTypeKey;
        this.monitor = monitor;
        this.orderSqlParserService = orderSqlParserService;
        this.executeResultService = executeResultService;
        this.sqlParser = SqlParserBuilder.getSqlParser();
        this.config = config;
        this.taskIdConsumer = taskIdConsumer;
        this.taskInfoProducer = taskInfoProducer;
        this.contextInfoConsumer = contextInfoConsumer;
        this.executeCommitType = executeCommitType;
        this.isActiveInstance = isActiveInstance;
        this.isActiveSchema = isActiveSchema;
        this.userId = userId;
        this.downloadFileMapper = downloadFileMapper;
        this.scriptName = scriptName;
        this.order = order;
        this.applyContent = order.getApply_content();
        this.maxSize = maxSize;
        this.consumerFail = consumerFail;
        this.waterMarkService = Resource.getBean(WaterMarkService.class);
        this.orderExecuteResult = new OrderExecuteResult(orderExecuteId, "", scriptName, message.getExecuteOrderModel().getSqlScriptType().getValue());
    }

    public WorkOrderExecuteConsumer(Queue<OrderSqlParse> queue,
                                    OrderTypeKey orderTypeKey,
                                    ExecuteOrderMessage message,
                                    DBRProgressMonitor monitor,
                                    OrderSqlParserService orderSqlParserService,
                                    SummerConfig config,
                                    String taskIdConsumer,
                                    WebAsyncTaskInfo taskInfoProducer,
                                    WebSQLContextInfo contextInfoConsumer,
                                    ExecuteCommitType executeCommitType,
                                    boolean isActiveInstance,
                                    boolean isActiveSchema,
                                    String userId,
                                    OrderDownloadFileMapper downloadFileMapper,
                                    String scriptName,
                                    Order order,
                                    int maxSize,
                                    AtomicReference<Boolean> consumerFail,
                                    OrderExecuteResultService executeResultService,
                                    Integer orderExecuteId,
                                    Integer schemaTaskId,
                                    Pair<OrderSchemaTask, BatchWorkOrderInstance> pair) {
        this.queue = queue;
        this.message = message;
        this.orderTypeKey = orderTypeKey;
        this.monitor = monitor;
        this.orderSqlParserService = orderSqlParserService;
        this.executeResultService = executeResultService;
        this.sqlParser = SqlParserBuilder.getSqlParser();
        this.config = config;
        this.taskIdConsumer = taskIdConsumer;
        this.taskInfoProducer = taskInfoProducer;
        this.contextInfoConsumer = contextInfoConsumer;
        this.executeCommitType = executeCommitType;
        this.isActiveInstance = isActiveInstance;
        this.isActiveSchema = isActiveSchema;
        this.userId = userId;
        this.downloadFileMapper = downloadFileMapper;
        this.scriptName = scriptName;
        this.order = order;
        this.applyContent = order.getApply_content();
        this.maxSize = maxSize;
        this.consumerFail = consumerFail;
        this.waterMarkService = Resource.getBean(WaterMarkService.class);
        this.orderExecuteResult = new OrderExecuteResult(orderExecuteId, "", scriptName, message.getExecuteOrderModel().getSqlScriptType().getValue());
        this.schemaTaskId = schemaTaskId;
        this.pair = pair;
    }

    public void run() throws DBException, InterruptedException {

        boolean isSuccess = true;

        long startNum = 0;

        List<OrderSqlParse> orderSqlParseList = new ArrayList<>();

        int queueCount = 0;
        try {

            int index = 0;

            while (true) {
                OrderSqlParse parse = queue.poll();
                if (index == 0 && parse != null) {
                    startNum = parse.getLine_number();
                }

                if (parse != null) {
                    queueCount++;

                    if (!executeOrderSqlParse(parse, orderSqlParseList)) {
                        isSuccess = false;

                        if (contextInfoConsumer.isInterrupted()) {
                            orderSqlParserPause(parse);
                            throw new InterruptedException("工单消费者出现中断错误，消费者终止。");
                        }

                        if (ExecuteCommitType.ERROR_ROLLBACK == executeCommitType) {
                            rollback();
                        }

                        if (ExecuteCommitType.ERROR_COMMIT == executeCommitType &&
                                contextInfoConsumer.getDataSource().getInfo().isExecuteErrorTransactionInterrupted()) {
                            commit();
                        }

                        if (ExecuteCommitType.ERROR_CONTINUE == executeCommitType) {
                            consumerFail.set(true);
                        }

                        if (ExecuteCommitType.ERROR_CONTINUE != executeCommitType) {
                            contextInfoConsumer.setTaskStatus(taskIdConsumer, WebAsyncTaskType.SQL_ERROR);
                            orderSqlParserPause(parse);

                            throw new DBException("工单消费者出现错误，消费者终止。");
                        }
                    } else if (WebAsyncTaskType.SQL_ERROR.equals(taskInfoProducer.getStatus()) ||
                            WebAsyncTaskType.SYS_ERROR.equals(taskInfoProducer.getStatus())) {

                        if (ExecuteCommitType.ERROR_ROLLBACK == executeCommitType) {
                            rollback();
                        }
                        break;
                    }
                } else if (taskInfoProducer.isOver()) {

                    // finished break
                    // sql,sys,cancel rollback
                    //判定生产者是否已经执行错误，如果有，则根据执行提交规则进行判定是否继续
                    if (WebAsyncTaskType.SQL_ERROR.equals(taskInfoProducer.getStatus()) ||
                            WebAsyncTaskType.SYS_ERROR.equals(taskInfoProducer.getStatus())) {
                        isSuccess = false;
                        if (ExecuteCommitType.ERROR_ROLLBACK == executeCommitType) {
                            rollback();
                        }
                    }
                    break;
                } else {
                    if (WebAsyncTaskType.WAIT_COMMIT.equals(taskInfoProducer.getStatus())) {
                        contextInfoConsumer.setTaskStatus(taskIdConsumer, WebAsyncTaskType.WAIT_COMMIT);
                        synchronized (queue) {
                            queue.notifyAll();
                        }
                    } else {
                        synchronized (queue) {
                            queue.wait(10000L);
                        }
                    }
                }

                synchronized (queue) {
                    if (queue.size() < maxSize) {
                        queue.notifyAll();
                    }
                }

                index++;
            }
        } finally {
            orderSqlParserService.batchUpdateSqlParse(orderSqlParseList, true);
            log.debug("累计出队列数：{}", queueCount);
            if (needCommit && !contextInfoConsumer.isInterrupted()) {
                commit();
            }

            uploadFile(isSuccess, startNum);
            synchronized (queue) {
                queue.notifyAll();
            }
            executeResultService.updateExecuteFailReason(orderExecuteResult);
        }

    }

    private static void sessionStatus(RecordSignType waitCommit) {
        StatParam param = new StatParam();
        param.setToken(HandlerCounter.getToken());
        param.setContainerId(HandlerCounter.getContainerId());
        RecordHandler.handle(RecordType.SESSION).appendRow(param, waitCommit);
    }

    private void uploadFile(boolean isSuccess, long startNum) {
        if (files != null && isSuccess) {
            if (startNum > 1) {
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("order_id", message.getExecuteOrderModel().getId());
                queryMap.put("script_name", scriptName);
                queryMap.put("execute_status", OrderSqlExecuteStatus.execute_success.getValue());
                queryMap.put("line_number", startNum);
                queryMap.put("is_skip", 0);
                if (orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT_BATCH) {
                    queryMap.put("order_schema_task_id", schemaTaskId);
                }
                List<OrderSqlParse> list = orderSqlParserService.querySqlParserExport(queryMap);
                if (list != null) {
                    for (OrderSqlParse sqlParse : list) {
                        if (StringUtils.isNotBlank(sqlParse.getFile_path())) {
                            String dirPath = null;
                            String fileName = null;
                            try {
                                if (sqlParse.getFile_path().contains("/")) {
                                    fileName = sqlParse.getFile_path().substring(sqlParse.getFile_path().lastIndexOf("/") + 1);
                                } else {
                                    fileName = sqlParse.getFile_path();
                                }
                                dirPath = Paths.get(Global.getEXPORT()).resolve(String.format("order_%s", message.getExecuteOrderModel().getId())).toUri().getPath();
                                File file = downloadFile(config.getPath().getDcBackend(), sqlParse.getFile_path(), dirPath, fileName);
                                files.add(file);
                                continue;
                            } catch (Exception e) {
                                log.error("导出工单下载文件失败！" + e.getMessage());
                                log.debug("重试下载！");
                                try {
                                    File file = downloadFile(config.getPath().getDcBackend(), sqlParse.getFile_path(), dirPath, fileName);
                                    files.add(file);
                                    continue;
                                } catch (Exception ex) {
                                    log.error("导出工单下载文件再次失败！重新下载！" + ex.getMessage());
                                    orderExecuteResult.setExecute_fail_reason(ex.getMessage());
                                }
                            }
                        }
                        try {
                            String sql = SqlTrimUtil.trimSqlDelimiter(applyContent.getDb_type(), sqlParse.getSql_content());
                            WorkOrderTransferResult orderTransferResult = getExportFile(config, applyContent, sql, null);
                            files.add(orderTransferResult.getFile());
                            sqlParse.setLog(orderTransferResult.getMessage());
                        } catch (Exception e) {
                            sqlParse.setLog(e.getMessage());
                            sqlParse.setExecute_status(OrderSqlExecuteStatus.execute_fail.getValue());
                        }
                    }

                }
            }
            try {
                String zipPath = Paths.get(Global.getEXPORT()).resolve(String.format("order_%s", message.getExecuteOrderModel().getId())).toUri().getPath();
                String fileName = String.format("%s.zip", order.getCode());
                if (orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT_BATCH) {
                    fileName = String.format("%s-%d.zip", order.getCode(), schemaTaskId);
                }
                zipPath = String.format("%s/%s", zipPath, fileName);
                String downloadLimit = applyContent.getDownload_limit();
                Map<String, Object> downloadMap = new HashMap<>();
                String downloadRule = "";
                if (org.apache.commons.lang3.StringUtils.isEmpty(downloadLimit)) {
                    downloadMap.put("download_limit", "");
                } else {
                    downloadMap.put("download_limit", Integer.valueOf(downloadLimit));
                }
                downloadMap.put("export_way", applyContent.getExport_way());
                downloadMap.put("order_relevance", applyContent.getLxfkzmd());
                downloadMap.put("encrypt_password", applyContent.getEncrypt_password());
                downloadRule = gson.toJson(downloadMap);
                WebDataTransferHelper.toZipAndUpload(
                        config.getPath().getDcBackend(),
                        applyContent.getEncrypt_password(),
                        ApiConfig.UPLOAD_RESULT.getPath(),
                        files,
                        zipPath,
                        downloadFileMapper,
                        fileName,
                        message.getExecuteOrderModel().getId(),
                        downloadRule,
                        userId);
            } catch (Exception e) {
                log.error(String.format("工单号：%s， zip上传文件失败", message.getExecuteOrderModel().getId()), e);
                orderExecuteResult.setExecute_fail_reason(e.getMessage());
            }

            for (File file : files) {
                if (file != null) {
                    file.delete();
                }
            }
            File tempFile = Paths.get(Global.getEXPORT()).resolve(String.format("order_%s", message.getExecuteOrderModel().getId())).toFile();
            if (tempFile != null) {
                com.dc.utils.http.FileUtil.deleteFile(tempFile);
            }
        }
    }

    private boolean executeOrderSqlParse(OrderSqlParse parse, List<OrderSqlParse> orderSqlParseList) {

        Sensitive sensitive = null;
        try {
            parse.setExecute_status(OrderSqlExecuteStatus.execute_in.getValue());
            orderSqlParserService.updateSqlParser(parse);

            if (contextInfoConsumer.isInterrupted()) {
                parse.setLog("用户中断执行");
                parse.setExecute_status(OrderSqlExecuteStatus.execute_interrupt.getValue());
            } else if (!isActiveInstance) {
                parse.setLog("数据源已被关闭！");
                parse.setExecute_status(OrderSqlExecuteStatus.execute_fail.getValue());
            } else if (!isActiveSchema) {
                parse.setLog("Schema已被删除");
                parse.setExecute_status(OrderSqlExecuteStatus.execute_fail.getValue());
            } else if (OrderTypeKey.EXPORT_SQL == orderTypeKey || OrderTypeKey.EOA_EXPORT_SQL == orderTypeKey) {
                sensitive = export(parse);
            } else {
                this.execute(parse);
            }
            sessionStatus(RecordSignType.WAIT_COMMIT);

        } catch (Exception e) {
            log.error("executeOrderSqlParse error", e);
            if (contextInfoConsumer.isInterrupted()) {
                parse.setLog("用户中断执行");
                parse.setExecute_status(OrderSqlExecuteStatus.execute_interrupt.getValue());
            } else {
                parse.setLog(e.getMessage());
                parse.setExecute_status(OrderSqlExecuteStatus.execute_fail.getValue());
            }
        } finally {
            orderSqlParseList.add(parse);
            orderSqlParserService.batchUpdateSqlParse(orderSqlParseList, false);
            if (!orderTypeKey.isPrivilege()) {
                SqlHistory sqlHistory = null;
                if (orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT_BATCH) {
                    sqlHistory = getSqlHistory(pair.getSecond().getSchemaInfo());
                    sqlHistory.setRecords(getSqlRecord(parse, pair.getSecond().getSchemaInfo()));
                } else {
                    sqlHistory = getSqlHistory();
                    sqlHistory.setRecords(getSqlRecord(parse));
                }

                if (sensitive != null) {
                    sqlHistory.getRecords().setOperationUser(sensitive.getOperationUser());
                    sqlHistory.getRecords().setApprovalUser(sensitive.getApprovalUser());
                    sqlHistory.getRecords().setEnableDesensitizeType(sensitive.getEnableDesensitizeType());
                    sqlHistory.setAuthTraceData(sensitive.getAuthTraceData());
                    sqlHistory.getRecords().setSensitiveSelect(Boolean.TRUE.equals(sensitive.getSensitiveSelect()));
                    if (sensitive.getSensitiveAuthDetail() != null && !sensitive.getSensitiveAuthDetail().isEmpty()) {
                        sqlHistory.setSqlId(sensitive.getSensitiveAuthDetail().get(0).getSqlId());
                    }
                    messageService.printLogMessage(contextInfoConsumer, sqlHistory, sensitive.getSensitiveAuthDetail(), null, null);
                } else {
                    messageService.printLogMessage(contextInfoConsumer, sqlHistory, null, null, null);
                }
            }
        }

        return OrderSqlExecuteStatus.execute_success.getValue().equals(parse.getExecute_status());
    }

    private SqlRecord getSqlRecord(OrderSqlParse parse) {
        List<ActionModel> actionList = null;
        try {
            // todo 传参需增加catalogName
            actionList = sqlParser.sqlParser(parse.getSql_content(), applyContent.getDb_type(), applyContent.getSchema_name(), "");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        SqlRecord sqlRecord = new SqlRecord();
        sqlRecord.setActions(CommonUtil.mergeActionList(actionList, applyContent.getDb_type()));
        sqlRecord.setMessage(parse.getLog());
        sqlRecord.setSql(parse.getSql_content());
        sqlRecord.setIsSuccess(true);
        sqlRecord.setRecordStatus(OrderSqlExecuteStatus.getSqlExecuteStatus(parse.getExecute_status()));
        sqlRecord.setOrderCode(order.getCode());
        return sqlRecord;
    }

    private SqlRecord getSqlRecord(OrderSqlParse parse, SchemaInfo schemaInfo) {
        List<ActionModel> actionList = null;
        try {
            // todo 传参需增加catalogName
            actionList = sqlParser.sqlParser(parse.getSql_content(), schemaInfo.getDb_type(), schemaInfo.getSchema_name(), "");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        SqlRecord sqlRecord = new SqlRecord();
        sqlRecord.setActions(CommonUtil.mergeActionList(actionList, schemaInfo.getDb_type()));
        sqlRecord.setMessage(parse.getLog());
        sqlRecord.setSql(parse.getSql_content());
        sqlRecord.setIsSuccess(true);
        sqlRecord.setRecordStatus(OrderSqlExecuteStatus.getSqlExecuteStatus(parse.getExecute_status()));
        sqlRecord.setOrderCode(order.getCode());
        return sqlRecord;
    }

    private SqlHistory getSqlHistory(SchemaInfo schemaInfo) {

        OperatorUserInfo operatorUserInfo = message.getOperatorUserInfo();
        ExecuteOrderModel executeOrderModel = message.getExecuteOrderModel();

        //执行历史
        SqlHistory sqlHistory = new SqlHistory();
        sqlHistory.setType(Long.valueOf(SqlHistoryType.HISTORY.getValue()));
        sqlHistory.setScriptId(Long.valueOf(order.getId()));
        sqlHistory.setUserId(operatorUserInfo.getUserId());
        sqlHistory.setName(operatorUserInfo.getName());
        sqlHistory.setRealName(operatorUserInfo.getRealName());
        sqlHistory.setOrganizationId(operatorUserInfo.getOrganizationId());
        sqlHistory.setOrganizationName(operatorUserInfo.getOrganizationName());
        sqlHistory.setIp(executeOrderModel.getIp());
        sqlHistory.setHostname(executeOrderModel.getHostname());
        sqlHistory.setConnectId(schemaInfo.getConnect_id());
        sqlHistory.setInstanceName(schemaInfo.getInstance_name());
        sqlHistory.setConnectDesc(schemaInfo.getConnection_desc());
        sqlHistory.setEnvironment(Long.valueOf(schemaInfo.getEnvironment()));
        sqlHistory.setDbType(schemaInfo.getDb_type());
        sqlHistory.setDbTypeZh(DatabaseType.of(schemaInfo.getDb_type()).getName());
        sqlHistory.setSchemaId(schemaInfo.getSchema_id());
        sqlHistory.setSchemaName(schemaInfo.getSchema_name());
        sqlHistory.setOrigin(orderTypeKey.getOriginType().getValue());
        if (Integer.valueOf(2).equals(applyContent.getOrigin())) {
            sqlHistory.setOriginZh(sqlHistory.getOriginZh() + " | API调用");
        }
        sqlHistory.setOrderRelevance(schemaInfo.getLxfkzmd());
        sqlHistory.setLabelId(schemaInfo.getLabel_id());
        sqlHistory.setLabelName(schemaInfo.getLabel_name());
        sqlHistory.setConnectUser(schemaInfo.getConnect_user());
        sqlHistory.setConnectionPattern(schemaInfo.getConnection_pattern());
        sqlHistory.setConnectionPatternZh(schemaInfo.getConnection_pattern_zh());
        sqlHistory.setSqlId(UUID.randomUUID().toString());
        sqlHistory.setUserCatalogIds(applyContent.getUser_catalog_ids());
        sqlHistory.setUserCatalog(applyContent.getUser_catalog());
        sqlHistory.setInstanceCatalogIds(schemaInfo.getInstance_catalog_ids());
        sqlHistory.setInstanceCatalog(schemaInfo.getInstance_catalog());
        try {
            sqlHistory.setSessionId(contextInfoConsumer.getExecutionContext().getProcessId());
        } catch (DBException ignored) {
        }

        return sqlHistory;
    }



    private SqlHistory getSqlHistory() {

        OperatorUserInfo operatorUserInfo = message.getOperatorUserInfo();
        ExecuteOrderModel executeOrderModel = message.getExecuteOrderModel();

        //执行历史
        SqlHistory sqlHistory = new SqlHistory();
        sqlHistory.setType(Long.valueOf(SqlHistoryType.HISTORY.getValue()));
        sqlHistory.setScriptId(Long.valueOf(order.getId()));
        sqlHistory.setUserId(operatorUserInfo.getUserId());
        sqlHistory.setName(operatorUserInfo.getName());
        sqlHistory.setRealName(operatorUserInfo.getRealName());
        sqlHistory.setOrganizationId(operatorUserInfo.getOrganizationId());
        sqlHistory.setOrganizationName(operatorUserInfo.getOrganizationName());
        sqlHistory.setIp(executeOrderModel.getIp());
        sqlHistory.setHostname(executeOrderModel.getHostname());
        sqlHistory.setConnectId(applyContent.getConnect_id());
        sqlHistory.setInstanceName(applyContent.getInstance_name());
        sqlHistory.setConnectDesc(applyContent.getConnection_desc());
        sqlHistory.setEnvironment(Long.valueOf(applyContent.getEnvironment()));
        sqlHistory.setDbType(applyContent.getDb_type());
        sqlHistory.setDbTypeZh(DatabaseType.of(applyContent.getDb_type()).getName());
        sqlHistory.setSchemaId(applyContent.getSchema_id());
        sqlHistory.setSchemaName(applyContent.getSchema_name());
        sqlHistory.setOrigin(orderTypeKey.getOriginType().getValue());
        if (Integer.valueOf(2).equals(applyContent.getOrigin())) {
            sqlHistory.setOriginZh(sqlHistory.getOriginZh() + " | API调用");
        }
        sqlHistory.setOrderRelevance(applyContent.getLxfkzmd());
        sqlHistory.setLabelId(applyContent.getLabel_id());
        sqlHistory.setLabelName(applyContent.getLabel_name());
        sqlHistory.setConnectUser(applyContent.getConnect_user());
        sqlHistory.setConnectionPattern(applyContent.getConnection_pattern());
        sqlHistory.setConnectionPatternZh(applyContent.getConnection_pattern_zh());
        sqlHistory.setSqlId(UUID.randomUUID().toString());
        sqlHistory.setUserCatalogIds(applyContent.getUser_catalog_ids());
        sqlHistory.setUserCatalog(applyContent.getUser_catalog());
        sqlHistory.setInstanceCatalogIds(applyContent.getInstance_catalog_ids());
        sqlHistory.setInstanceCatalog(applyContent.getInstance_catalog());
        try {
            sqlHistory.setSessionId(contextInfoConsumer.getExecutionContext().getProcessId());
        } catch (DBException ignored) {
        }

        return sqlHistory;
    }

    private Sensitive export(OrderSqlParse parse) throws Exception {
        Sensitive sensitive = new Sensitive();
        if (files == null) {
            files = new ArrayList<>();
        }
        long startTime = System.currentTimeMillis();
        String message;
        if ("SELECT".equalsIgnoreCase(parse.getSql_type())) {
            String sql = parse.getSql_content();
            sql = SqlTrimUtil.trimSqlDelimiter(applyContent.getDb_type(), sql);
            WorkOrderTransferResult orderTransferResult;
            try {
                orderTransferResult = getExportFile(config, applyContent, sql, sensitive);
                String uploadPath = WebDataTransferHelper.toUpload(
                        config.getPath().getDcBackend(),
                        ApiConfig.UPLOAD_RESULT.getPath(),
                        orderTransferResult.getFile(),
                        userId);
                parse.setFile_path(uploadPath);
                files.add(orderTransferResult.getFile());
                message = orderTransferResult.getMessage();
                parse.setExecute_status(OrderSqlExecuteStatus.execute_success.getValue());
                long endTime = System.currentTimeMillis();
                if ("SELECT".equalsIgnoreCase(parse.getSql_type())) {
                    parse.setLog(String.format("%s，耗时：[%s]ms.", message, (endTime - startTime)));
                    parse.setSql_content(orderTransferResult.getQueryText());
                }
            } catch (Exception e) {
                if (contextInfoConsumer.isInterrupted()) {
                    log.error("语句执行中断！", e);
                    parse.setLog("用户中断执行");
                    parse.setExecute_status(OrderSqlExecuteStatus.execute_interrupt.getValue());
                } else {
                    log.error("语句执行失败！", e);
                    parse.setLog(e.getMessage());
                    parse.setExecute_status(OrderSqlExecuteStatus.execute_fail.getValue());
                }
            }
        } else if ("SET".equalsIgnoreCase(parse.getSql_type())) { //SET只执行，不生成文件。
            ParserParamDto paramDTO = new ParserParamDto();
            paramDTO.setSql(parse.getSql_content());
            if (orderTypeKey.isBatchScript()) {
                paramDTO.setDbType(pair.getSecond().getSchemaInfo().getDb_type());
                paramDTO.setConnectId(pair.getSecond().getSchemaInfo().getConnect_id());
                paramDTO.setSchemaId(pair.getSecond().getSchemaInfo().getSchema_id());
            } else {
                paramDTO.setDbType(applyContent.getDb_type());
                paramDTO.setConnectId(applyContent.getConnect_id());
                paramDTO.setSchemaId(applyContent.getSchema_id());
            }
            paramDTO.setUserId(userId);

            //调iceage接口，返回use database，如果是use database，就忽略。如果不是，就执行。
            WebSQLParserResult webSQLParserResult = parserSqlClient.executeSql(Client.getClient(config.getPath().getDcIceage()), paramDTO).get(0);
            if (StringUtils.isEmpty(webSQLParserResult.getUseDatabase())) {
                //执行
                this.execute(parse);
            } else { //如果是切换数据库，不执行。
                parse.setLog("已被忽略");
                parse.setExecute_status(OrderSqlExecuteStatus.execute_success.getValue());
            }

        } else {
            parse.setLog("已被忽略");
            parse.setExecute_status(OrderSqlExecuteStatus.execute_success.getValue());
        }
        return sensitive;
    }

    private void execute(OrderSqlParse parse) throws Exception {

        SQLQueryType sqlQueryType = SQLQueryType.of(parse.getSql_type());
        String sql = parse.getSql_content();

        if (orderTypeKey.isBatchScript()) {
            if (sqlQueryType == SQLQueryType.INSERT &&
                    DatabaseType.SQL_SERVER.getValue().equals(pair.getSecond().getSchemaInfo().getDb_type()) &&
                    orderTypeKey.isImportData()) {
                String[] names = applyContent.getTable_name().split("\\.");
                if (names.length > 1) {
                    String tableName = String.format("%s.%s.%s", pair.getSecond().getSchemaInfo().getSchema_name(), names[0], names[1]);
                    if (!isIncrementField.containsKey(tableName)) {
                        isIncrementField.put(tableName, IdentityInsertUtil.existsIncrementField(monitor, contextInfoConsumer.getExecutionContext(), parse.getSql_content(), tableName));
                    }
                    if (Boolean.TRUE.equals(isIncrementField.get(tableName))) {
                        sql = IdentityInsertUtil.insert4Sqlserver(tableName, parse.getSql_content());
                    }
                }
            }
        } else {
            if (sqlQueryType == SQLQueryType.INSERT &&
                    DatabaseType.SQL_SERVER.getValue().equals(applyContent.getDb_type()) &&
                    orderTypeKey.isImportData()) {
                String[] names = applyContent.getTable_name().split("\\.");
                if (names.length > 1) {
                    String tableName = String.format("%s.%s.%s", applyContent.getSchema_name(), names[0], names[1]);
                    if (!isIncrementField.containsKey(tableName)) {
                        isIncrementField.put(tableName, IdentityInsertUtil.existsIncrementField(monitor, contextInfoConsumer.getExecutionContext(), parse.getSql_content(), tableName));
                    }
                    if (Boolean.TRUE.equals(isIncrementField.get(tableName))) {
                        sql = IdentityInsertUtil.insert4Sqlserver(tableName, parse.getSql_content());
                    }
                }
            }
        }

        if (sqlQueryType.isSelect()) {
            parse.setLog("已被忽略");
            parse.setExecute_status(OrderSqlExecuteStatus.execute_success.getValue());

        } else {
            ValidExecuteModel validExecuteModel = new ValidExecuteModel();
            validExecuteModel.setSql(sql);
            validExecuteModel.setOperation(parse.getSql_type());
            String backupModel = parse.getBackup_model();
            if (StringUtils.isNotBlank(backupModel)) {
                validExecuteModel.setBackupModel(JSON.parseObject(backupModel, BackupModel.class));
            }
            String privilegeModel = parse.getPrivilege_model();
            if (StringUtils.isNotBlank(privilegeModel) && !privilegeModel.equalsIgnoreCase("null")) {
                validExecuteModel.setPrivilegeModel(JSON.parseObject(privilegeModel, PrivilegeModel.class));
            }
            String privilegeExpire = parse.getPrivilege_expire();
            if (StringUtils.isNotBlank(privilegeExpire)) {
                validExecuteModel.setPrivilegeExpire(privilegeExpire);
            }
            validExecuteModel.setUserId(order.getApply_user());
            SqlHistory sqlHistory = new SqlHistory();
            sqlHistory.setUserId(message.getOperatorUserInfo().getUserId());
            sqlHistory.setName(message.getOperatorUserInfo().getName());
            sqlHistory.setSqlId(UUID.randomUUID().toString());
            if (orderTypeKey.isScriptChanges()) {
                sqlHistory.setOrigin(OriginType.SCRIPT.getValue());
                sqlHistory.setOriginZh("脚本变更工单");
            } else if (orderTypeKey == OrderTypeKey.PRIVILEGE_CHANGE) {
                sqlHistory.setOrigin(OriginType.PRIVILEGE_CHANGE.getValue());
                sqlHistory.setOriginZh("账号变更申请");
            } else if (orderTypeKey == OrderTypeKey.ACCOUNT_APPLICATION) {
                sqlHistory.setOrigin(OriginType.ACCOUNT_APPLICATION.getValue());
                sqlHistory.setOriginZh("数据库用户申请");
            }
            validExecuteModel.setSqlHistory(sqlHistory);

            List<WebSQLQueryResult> webSQLQueryResults = contextInfoConsumer.getProcessor().processQuery(
                    monitor,
                    Collections.singletonList(validExecuteModel),
                    WebDataFormat.document,
                    null,
                    DBCExecutionPurpose.USER_SCRIPT,
                    false,
                    batchExecuteModel -> ChainBuilder.none(),
                    batchExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new BackupModifyChain(monitor,
                                    contextInfoConsumer,
                                    batchExecuteModel.getOperation(),
                                    batchExecuteModel.getBackupModel(),
                                    batchExecuteModel.getUserId(),
                                    sqlSessionTemplate,
                                    batchExecuteModel.getSql())),
                    batchExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new BackupInsertChain(
                                    monitor,
                                    contextInfoConsumer,
                                    batchExecuteModel.getOperation(),
                                    batchExecuteModel.getBackupModel(),
                                    batchExecuteModel.getUserId(),
                                    sqlSessionTemplate,
                                    batchExecuteModel.getSql()))
                            .addChain(new BackupOracleDropChain(
                                    monitor,
                                    contextInfoConsumer,
                                    batchExecuteModel.getBackupModel(),
                                    batchExecuteModel.getSql(),
                                    batchExecuteModel.getUserId(),
                                    batchExecuteModel.getOperation()))
                            .addChain(new BackupTransactionBatchChain(
                                    monitor,
                                    contextInfoConsumer,
                                    batchExecuteModel.getBackupModel(),
                                    orderTypeKey.getOriginType().getValue(),
                                    sqlSessionTemplate,
                                    batchExecuteModel.getOperation(),
                                    batchExecuteModel.getUserId(),
                                    contextInfoConsumer.getAutoCommit()))
                            .addChain(new PrivilegesNoteChain(contextInfoConsumer, batchExecuteModel.getPrivilegeModel(), validExecuteModel.getPrivilegeExpire())),
                    false,
                    false,
                    null);

            WebSQLQueryResult webSQLQueryResult = webSQLQueryResults.get(0);
            parse.setLog(webSQLQueryResult.getMessage());
            parse.setExecute_status(webSQLQueryResult.getStatus());
            parse.setBackup_warning(webSQLQueryResult.getBackupWarning());

            OrderSqlExecuteStatus orderSqlExecuteStatus = OrderSqlExecuteStatus.getOrderSqlExecuteStatus(webSQLQueryResult.getStatus());
            if (orderSqlExecuteStatus == OrderSqlExecuteStatus.execute_interrupt) {
                parse.setExecute_status(OrderSqlExecuteStatus.execute_interrupt.getValue());
            }

        }

    }


    private WorkOrderTransferResult getExportFile(SummerConfig config, OrderApplyContent applyContent, String sql, Sensitive sensitive) throws DBException {
        DataTransferProcessorDescriptor processor = DataTransferRegistry.getInstance()
                .getProcessor(FileType.getExportTypeByCode(applyContent.getExport_type()).getProcessorFullId());
        SqlExportMessage sqlExportMessage = new SqlExportMessage();
        sqlExportMessage.setToken(contextInfoConsumer.getToken());
        sqlExportMessage.setExportType(FileType.getExportTypeByCode(applyContent.getExport_type()));
        sqlExportMessage.setPageSelected(PageSelectedType.ALL_PAGES);
        sqlExportMessage.setFileCharset("UTF-8");
        sqlExportMessage.setTextIdentifier(TextIdentifierType.of(applyContent.getText_identifier()));
        sqlExportMessage.setLineDelimiter(LineDelimiterType.of(applyContent.getLine_delimiter()));
        sqlExportMessage.setColumnDelimiter(ColumnDelimiterType.of(applyContent.getColumn_delimiter()));
        sqlExportMessage.setOtherDelimiter(applyContent.getOther_delimiter());
        sqlExportMessage.setExcelDatetimeFormat(applyContent.getExcel_datetime_format());
        sqlExportMessage.setExcelUseOriginalFormat(applyContent.getExcel_use_original_format());
        sqlExportMessage.setExcelUseNumberFormat(applyContent.isExcel_use_number_format());

        WaterMark waterMark = waterMarkService.getRealWaterMark(userId);
        if (waterMark != null) {
            sqlExportMessage.setWatermarkContent(waterMark.getContent());
            sqlExportMessage.setWatermarkAngle(waterMark.getAngle());
        }

        ParserParamDto preCheckParamDTO = new ParserParamDto();
        preCheckParamDTO.setSql(sql);
        preCheckParamDTO.setDbType(applyContent.getDb_type());
        preCheckParamDTO.setConnectId(applyContent.getConnect_id());
        preCheckParamDTO.setSchemaId(applyContent.getSchema_id());
        preCheckParamDTO.setUserId(userId);
        preCheckParamDTO.setIsVerify(ParserExecuteType.makeIsVerify(ParserExecuteType.DATA_MASK_PARSER));
        preCheckParamDTO.setOrderMaskLevel(applyContent.getEnable_desensitize_type());

        WebSQLParserResult singleExecuteSql = new WebSQLParserResult();
        try {
            singleExecuteSql = parserSqlClient.executeSql(Client.getClient(config.getPath().getDcIceage()), preCheckParamDTO).get(0);
            List<SensitiveAuthDetail> sensitiveAuthDetail = formatSensitiveAuthDetail(singleExecuteSql);
            String operationUser = message.getExecuteEvent().getOperationUser();
            String approvalUser = message.getExecuteEvent().getApprovalUser();
            if (sensitive != null) {
                sensitive.setOperationUser(operationUser);
                sensitive.setApprovalUser(approvalUser);
                sensitive.setEnableDesensitizeType(String.valueOf(applyContent.getEnable_desensitize_type()));
                sensitive.setSensitiveAuthDetail(sensitiveAuthDetail);
                if (singleExecuteSql.getMaskRules() != null && !singleExecuteSql.getMaskRules().isEmpty()) {
                    sensitive.setAuthTraceData(JSON.toJSONString(singleExecuteSql.getMaskRules()));
                } else {
                    sensitive.setAuthTraceData(null);
                }
                sensitive.setSensitiveSelect(singleExecuteSql.getDataMask() != null && !singleExecuteSql.getDataMask().isEmpty());
            }
        } catch (Exception e) {
            log.error("调用parser接口报错：" + e.getMessage());
            throw e;
        }

        boolean desenesAndBackstopEnabled = DefaultSwitchParamType.defaultOpen(applyContent.getSensitive_data_protection_desensitization()).isEnabled();

        GradedClassifiedModel gradedClassifiedModel = singleExecuteSql.getGradedClassifiedModel();
        gradedClassifiedModel.setDesens(desenesAndBackstopEnabled);
        gradedClassifiedModel.setBackstop(desenesAndBackstopEnabled);

        DataDesensitizeProcessor desensitizeData = new DataDesensitizeProcessor(
                contextInfoConsumer.getDataSource(),
                1,
                singleExecuteSql.getDataMask(),
                applyContent.getSymbol(),
                singleExecuteSql.getNodeModel(),
                gradedClassifiedModel,
                null);

        WebDataTransfer transfer = new WebDataTransfer(String.valueOf(message.getExecuteOrderModel().getId()), contextInfoConsumer);
        WorkOrderTransferResult orderTransferResult = transfer.exportDataBySql(
                monitor,
                processor,
                sqlExportMessage,
                new WebDataTransferName.OrderZipName(),
                new WebDataTransferName.OrderName(),
                true,
                true,
                sql,
                desensitizeData,
                applyContent.getSingle_sql_export_num(), applyContent.getConsole(), applyContent.getDb_name(), false);

        if (sensitive != null && orderTransferResult.getSensitiveSelect() != null) {
            sensitive.setSensitiveSelect(orderTransferResult.getSensitiveSelect());
        }

        return orderTransferResult;
    }


    public File downloadFile(String backendPath, String filePath, String dirPath, String fileName) throws Exception {
        String url = backendPath + ApiConfig.DOWNLOAD.getPath() + filePath;
        url = FileUtil.buildDownloadUrl(url, filePath);
        String file = com.dc.utils.http.FileUtil.downloadHttpUrl(url, dirPath, fileName);
        return new File(file);
    }


    public void commit() {
        try {
            if (!contextInfoConsumer.getDataSource().getInfo().supportsTransactions()) {
                log.info(String.format("当前工单【%s】的数据库不支持事务", order.getId()));
                return;
            }
            @SQL final String sql = "COMMIT";
            OrderSqlParse orderSqlParse = new OrderSqlParse();
            orderSqlParse.setSql_content(sql);
            orderSqlParse.setSql_type(sql);
            this.execute(orderSqlParse);
            sessionStatus(RecordSignType.NONE_COMMIT);
        } catch (Exception e) {
            log.error("commit error.", e);
            orderExecuteResult.setExecute_fail_reason(e.getMessage());
        }
    }

    public void rollback() {
        try {
            if (!contextInfoConsumer.getDataSource().getInfo().supportsTransactions()) {
                log.info(String.format("当前工单【%s】的数据库不支持事务", order.getId()));
                return;
            }
            @SQL final String sql = "ROLLBACK";
            OrderSqlParse orderSqlParse = new OrderSqlParse();
            orderSqlParse.setSql_content(sql);
            orderSqlParse.setSql_type(sql);
            this.execute(orderSqlParse);
            sessionStatus(RecordSignType.NONE_COMMIT);
            needCommit = false;
        } catch (Exception e) {
            log.error("rollback error.", e);
            orderExecuteResult.setExecute_fail_reason(e.getMessage());
        }
    }

    public void orderSqlParserPause(OrderSqlParse orderSqlParse) {
        orderSqlParserService.updateAllPauseSqlParse(orderSqlParse);
    }

    private List<SensitiveAuthDetail> formatSensitiveAuthDetail(WebSQLParserResult singleExecuteSql) {
        if (singleExecuteSql != null) {
            List<SensitiveAuthDetail> list = new ArrayList<>();
            if (singleExecuteSql.getDataMask() != null && !singleExecuteSql.getDataMask().isEmpty()) {
                List<DataMask> dataMasks = singleExecuteSql.getDataMask();
                for (DataMask dataMask : dataMasks) {
                    SensitiveAuthDetail sad = new SensitiveAuthDetail();
                    sad.setSqlId(singleExecuteSql.getSqlId());
                    sad.setColumnName(dataMask.getColumnName());
                    sad.setTableName(dataMask.getShowTableName());
                    sad.setSchemaName(dataMask.getSchemaName());
                    sad.setSchemaId(dataMask.getSchemaId());
                    sad.setDbType(dataMask.getDbType());
                    sad.setEnvironment(dataMask.getEnvironment());
                    sad.setConnectId(dataMask.getConnectId());
                    sad.setConnectionDesc(dataMask.getConnectionDesc());
                    sad.setInstanceName(dataMask.getInstanceName());
                    sad.setDesensitizeRuleId(dataMask.getDesensitizeRuleId());
                    sad.setDesensitizeRuleName(dataMask.getDesensitizeRuleName());
                    sad.setDistinguishRuleId(dataMask.getDistinguishRuleId());
                    sad.setDistinguishRuleName(dataMask.getDistinguishRuleName());
                    sad.setSensitiveLevelId(dataMask.getSensitiveLevelId());
                    sad.setSensitiveLevelColor(dataMask.getSensitiveLevelColor());
                    sad.setSensitiveLevelName(dataMask.getSensitiveLevelName());
                    sad.setAuthSensitive(dataMask.getAuthSensitive());
                    sad.setUserId(userId);
                    sad.setName(null);
                    sad.setIsDelete(0L);
                    sad.setGmtCreate(System.currentTimeMillis());
                    sad.setGmtModified(System.currentTimeMillis());
                    sad.setGroupByTable(String.format("%s^%s^%s", singleExecuteSql.getSqlId(), dataMask.getSchemaId(), dataMask.getShowTableName()));
                    sad.setOrderRelevance(applyContent.getLxfkzmd());
                    sad.setIp(message.getExecuteOrderModel().getIp());
                    list.add(sad);
                }
                return list;
            } else {
                return null;
            }
        } else {
            log.info("parser返回为空");
            return null;
        }
    }

}
