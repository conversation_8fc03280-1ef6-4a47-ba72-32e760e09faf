package com.dc.summer.service;

import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.AsyncExecuteExportMessage;
import com.dc.summer.model.data.message.AsyncExecuteMessage;
import com.dc.summer.model.data.message.AsyncInterruptMessage;
import com.dc.summer.model.data.result.AsyncExecuteExportResult;

public interface AsyncExecuteService {

    WebAsyncTaskInfo asyncExecuteTask(AsyncExecuteMessage message);

    void asyncExecuteExport(AsyncExecuteExportMessage message);

}
