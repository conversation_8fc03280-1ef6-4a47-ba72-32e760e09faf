package com.dc.workorder.service.check;

import com.dc.proxy.model.data.message.HiveMetaDataMessage;
import com.dc.proxy.model.result.HiveResult;
import com.dc.proxy.service.DatabaseService;
import com.dc.proxy.service.ExecuteService;
import com.dc.proxy.service.MetaDataService;
import com.dc.repository.mysql.column.*;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSqlParse;
import com.dc.repository.mysql.model.WorkOrderScript;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.database.*;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.springboot.core.model.type.PreCheckStatus;
import com.dc.springboot.core.model.type.SqlScriptType;
import com.dc.utils.CommonUtils;
import com.dc.utils.PairList;
import com.dc.workorder.model.exception.DataFormatException;
import com.dc.workorder.utils.OrderSqlParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class WorkOrderCheckSqlPrivilege extends WorkOrderCheckSql {

    protected final ExecuteService executeService = Resource.getBeanRequireNonNull(ExecuteService.class);

    protected final DatabaseService databaseService = Resource.getBeanRequireNonNull(DatabaseService.class);

    private final MetaDataService metaDataService = Resource.getBeanRequireNonNull(MetaDataService.class);

    protected final ConnectionConfig connectionConfig;

    protected Long lineNumber = 0L;

    protected final List<OrderSqlParse> orderSqlParses = new ArrayList<>();

    public WorkOrderCheckSqlPrivilege(OriginType originType, List<ParserExecuteType> parserExecuteTypes, ConnectionConfig connectionConfig) {
        super(originType, parserExecuteTypes);
        this.connectionConfig = connectionConfig;
    }

    @Override
    public PreCheckStatus parseContentAndInsert(Order order, SqlScript sqlScript, WorkOrderScript workOrderScript) {
        try {
            Integer orderId = order.getId();
            String scriptName = sqlScript.getName();
            OrderApplyContent applyContent = order.getApply_content();
            Integer dbType = applyContent.getDb_type();
            boolean isSkipPrecheck = null != applyContent.getIs_skip_precheck() && applyContent.getIs_skip_precheck() == 1;
            log.info("Execute SQL Split for Order：[{}]", orderId);

            generateOrderSqlParse(applyContent.getResource_list(), applyContent.getBase_info(), sqlScript.getAuthGrant(), sqlScript.getAuthRevoke(), orderId, scriptName, dbType);
            if (orderSqlParses.isEmpty()) {
                throw new DataFormatException("文件解析失败(数据格式错误)");
            }

            PairList<OrderSqlParse, WebSQLParserResult> pairList = new PairList<>(new ArrayList<>(), new ArrayList<>());

            for (OrderSqlParse prepared : orderSqlParses) {

                if (sqlScript instanceof RollbackSqlScript) {
                    prepared.setType(SqlScriptType.ROLLBACK_SCRIPT.getValue());
                } else {
                    prepared.setType(SqlScriptType.ORIGIN_SCRIPT.getValue());
                }

                prepared.setScript_name(scriptName);
                if (isSkipPrecheck) {
                    pairList.add(prepared, new WebSQLParserResult());
                } else {
                    pairList.add(prepared, preCheckParser(prepared.getSql_content(), order, applyContent));
                }
                orderSqlParserService.insertBatchMaxSize(pairList.getFirstList(), () -> insertBefore(pairList, applyContent), () -> insertAfter(pairList, order));
            }

            //批量存
            if (!pairList.getFirstList().isEmpty()) {
                orderSqlParserService.insertBatchAllEntity(pairList.getFirstList(), () -> insertBefore(pairList, applyContent), () -> insertAfter(pairList, order));
                pairList.getSecondList().clear();
            }
        } catch (Exception e) {
            log.error("ParseScriptContent error.", e);
            workOrderScript.setCheck_fail_reason(e.getMessage());
            return PreCheckStatus.Pending;
        } finally {
            orderSqlParses.clear();
        }
        return verifyFailTotal == 0 ? PreCheckStatus.PASS : PreCheckStatus.FAIL;

    }

    protected void generateOrderSqlParse(List<ResourceContent> resourceContents, BaseInfo baseInfo, AuthGrant authGrant, AuthRevoke authRevoke, Integer orderId, String scriptName, Integer dbType) {

        for (ResourceContent resource : resourceContents) {

            String username = resource.getUsernameWithQuoteCharacters();

            //获取 字典类型的对象
            List<ResourceObject> objects = Stream.concat(authGrant.getObject_auth().stream(), authRevoke.getObject_auth().stream())
                    .map(o -> new ResourceObject(o.getSchema_name(), o.getObject_name())).distinct().collect(Collectors.toList());

            ObjectTypeMessage objectTypeMessage = new ObjectTypeMessage();
            objectTypeMessage.setObjects(objects);
            objectTypeMessage.setConnectionConfig(connectionConfig);
            List<ResourceObject> directoryObjects = databaseService.getDirectoryObject(objectTypeMessage);

            for (ObjectAuth objectAuth : authGrant.getObject_auth()) {
                //GRANT SELECT ON "schema"."table" TO "user" WITH GRANT OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(objectAuth.getPrivilege())
                        .append(" ON ");

                int cur = 0;
                while (cur < directoryObjects.size()) {
                    ResourceObject currentObj = directoryObjects.get(cur);
                    if (StringUtils.isNotBlank(currentObj.getOwner())) {
                        if (currentObj.getOwner().equals(objectAuth.getSchema_name()) && currentObj.getName().equals(objectAuth.getObject_name())) {
                            break;
                        }
                    } else {
                        if (currentObj.getName().equals(objectAuth.getObject_name())) {
                            break;
                        }
                    }
                    cur++;
                }

                if (cur < directoryObjects.size()) {
                    sqlBuilder.append("DIRECTORY ");
                }

                sqlBuilder.append(objectAuth.getSchemaObject())
                        .append(" TO ")
                        .append(username);

                if (objectAuth.isGrantable()) {
                    sqlBuilder.append(" WITH GRANT OPTION");
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sqlBuilder.toString(), lineNumber++, objectAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }
            StringBuilder roles = new StringBuilder();
            for (RoleAuth roleAuth : authGrant.getRole_auth()) {
                //GRANT "role" TO "user" WITH ADMIN OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(roleAuth.getGranted_role())
                        .append(" TO ")
                        .append(username);
                if (roleAuth.isAdminOption()) {
                    sqlBuilder.append(" WITH ADMIN OPTION");
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sqlBuilder.toString(), lineNumber++, roleAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);

                if (roleAuth.getDefault_role() == 1) {
                    roles.append(roleAuth.getGranted_role()).append(",");
                }
            }
            if (roles.length() > 0) {
                roles.deleteCharAt(roles.length() - 1);

                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, "ALTER USER " + username + " DEFAULT ROLE " + roles, lineNumber++, null);
                orderSqlParses.add(orderSqlParse);
            }

            for (SysAuth sysAuth : authGrant.getSys_auth()) {
                String sql = String.format("GRANT %s TO %s", sysAuth.getPrivilege(), username);
                if (sysAuth.getAdmin_option() == 1) {
                    sql += " WITH ADMIN OPTION";
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, sysAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }

            for (TsQuota tsQuota : authGrant.getTs_quotas()) {
                String sql = String.format("ALTER USER %s QUOTA %s ON %s", username, tsQuota.getQuotaSize(), tsQuota.getTablespace_name());
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, tsQuota.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }

            if (!authGrant.getSchema_auth().isEmpty()) {
                orderSqlParses.addAll(changeSchemaPrivilegeToSql(authGrant.getSchema_auth(), username, "GRANT", orderId, scriptName));
            }

            if (!authGrant.getOther_auth().isBlank()) {
                ParseScriptMessage message = new ParseScriptMessage();
                message.setDatabaseType(dbType);
                message.setScript(authGrant.getOther_auth());
                WebSQLScriptInfo webSQLScriptInfo = executeService.parseScript(message);
                for (WebSQLQueryInfo query : CommonUtils.safeList(webSQLScriptInfo.getQueries())) {
                    String sql = query.getText();
                    OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, null);
                    orderSqlParses.add(orderSqlParse);
                }
            }

            for (ObjectAuth objectAuth : authRevoke.getObject_auth()) {
                StringBuilder sqlBuilder = new StringBuilder("REVOKE ")
                        .append(objectAuth.getPrivilege())
                        .append(" ON ");

                int cur = 0;
                while (cur < directoryObjects.size()) {
                    ResourceObject currentObj = directoryObjects.get(cur);
                    if (StringUtils.isNotBlank(currentObj.getOwner())) {
                        if (currentObj.getOwner().equals(objectAuth.getSchema_name()) && currentObj.getName().equals(objectAuth.getObject_name())) {
                            break;
                        }
                    } else {
                        if (currentObj.getName().equals(objectAuth.getObject_name())) {
                            break;
                        }
                    }
                    cur++;
                }

                if (cur < directoryObjects.size()) {
                    sqlBuilder.append("DIRECTORY ");
                }

                sqlBuilder.append(objectAuth.getSchemaObject())
                        .append(" FROM ")
                        .append(username);

                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sqlBuilder.toString(), lineNumber++, null);
                orderSqlParses.add(orderSqlParse);
            }

            for (RoleAuth roleAuth : authRevoke.getRole_auth()) {
                String sql = String.format("REVOKE %s FROM %s", roleAuth.getGranted_role(), username);
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, null);
                orderSqlParses.add(orderSqlParse);
            }

            for (SysAuth sysAuth : authRevoke.getSys_auth()) {
                String sql = String.format("REVOKE %s FROM %s", sysAuth.getPrivilege(), username);
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, null);
                orderSqlParses.add(orderSqlParse);
            }

            for (TsQuota tsQuota : authRevoke.getTs_quotas()) {
                String sql = String.format("ALTER USER %s QUOTA 0M ON %s", username, tsQuota.getTablespace_name());
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, null);
                orderSqlParses.add(orderSqlParse);
            }

            if (!authRevoke.getSchema_auth().isEmpty()) {
                orderSqlParses.addAll(changeSchemaPrivilegeToSql(authRevoke.getSchema_auth(), username, "REVOKE", orderId, scriptName));
            }

        }

    }

    protected List<OrderSqlParse> changeSchemaPrivilegeToSql(List<SchemaAuth> schemaAuths, String username, String sqlType, Integer orderId, String scriptName) {
        List<OrderSqlParse> orderSqlParses = new ArrayList<>();
        HiveMetaDataMessage allOfTablesMessage = new HiveMetaDataMessage();
        allOfTablesMessage.setConnectionConfig(connectionConfig);
        allOfTablesMessage.setPattern("%");
        allOfTablesMessage.setTableNamePattern("%");
        allOfTablesMessage.setTypes(List.of("TABLE"));
        int limit = 10000;
        allOfTablesMessage.setLimit(limit);
        allOfTablesMessage.setOffset(0);

        for (SchemaAuth schemaAuth : schemaAuths) {

            String schemaName = schemaAuth.getSchema_name();
            allOfTablesMessage.setSchemaPattern(schemaName.toUpperCase(Locale.ROOT));
            // 拆分权限

            boolean hasObj = false;
            while (true) {
                HiveResult allOfTablesInSchema = metaDataService.hiveMetadata(allOfTablesMessage);
                int total = allOfTablesInSchema.getTotal();

                List<String> tableNames = allOfTablesInSchema.getRecords().stream().map(r -> (String) r.get("object_name")).collect(Collectors.toList());
                for (String tableName : tableNames) {
                    String sql;
                    if (sqlType.equals("GRANT")) {
                        sql = String.format("GRANT %s ON %s.\"%s\" TO %s", schemaAuth.getPrivilege(), schemaName, tableName, username);
                    } else if (sqlType.equals("REVOKE")) {
                        sql = String.format("REVOKE %s ON %s.\"%s\" FROM %s", schemaAuth.getPrivilege(), schemaName, tableName, username);
                    } else {
                        continue;
                    }
                    if (schemaAuth.getGrantable() == 1) {
                        sql += " WITH GRANT OPTION";
                    }
                    hasObj = true;
                    OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, schemaAuth.getPrivilege_expire());
                    orderSqlParses.add(orderSqlParse);
                }
                if (total < limit) {
                    break;
                }
                allOfTablesMessage.setOffset(allOfTablesMessage.getOffset() + limit);
            }

            if (!hasObj) {
                String sql = "";
                if (sqlType.equals("GRANT")) {
                    sql = String.format("GRANT %s ON %s.%s TO %s", schemaAuth.getPrivilege(), schemaName, "", username);
                } else if (sqlType.equals("REVOKE")) {
                    sql = String.format("REVOKE %s ON %s.%s FROM %s", schemaAuth.getPrivilege(), schemaName, "", username);
                } else {
                    continue;
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, schemaAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }

            allOfTablesMessage.setOffset(0);
        }
        return orderSqlParses;
    }

}
