package com.dc.parser.ext.oracle.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OracleHintParser {

    /**
     * 定义一个灵活的正则表达式来匹配一个Hint
     * (\w+)            : 捕获组1, 匹配Hint名称 (如 PARALLEL, FULL)
     * (?:\((.*?)\))?   : 一个可选的非捕获组, 用于匹配括号和其中的所有内容
     * \(           : 匹配左括号
     * (.*?)        : 捕获组2, 非贪婪模式匹配括号内的所有字符 (参数字符串)
     * \)           : 匹配右括号
     * ?            : 表示整个括号部分是可选的
     */
    private static final Pattern HINT_PATTERN = Pattern.compile("(\\w+)(?:\\((.*?)\\))?");

    public static List<ParsedHint> parse(String hintComment) {
        List<ParsedHint> parsedHints = new ArrayList<>();

        // 1. 清理字符串，获取Hint核心内容
        if (hintComment == null || !hintComment.startsWith("/*+") || !hintComment.endsWith("*/")) {
            System.err.println("无效的Hint注释格式。");
            return parsedHints;
        }
        String hintContent = hintComment.substring(3, hintComment.length() - 2).trim();

        // 2. 使用正则表达式查找所有匹配的Hint
        Matcher matcher = HINT_PATTERN.matcher(hintContent);
        while (matcher.find()) {
            // 3. 提取Hint名称和参数字符串
            String hintName = matcher.group(1);
            String argsString = matcher.group(2); // 可能为 null，如果没有括号

            ParsedHint hint = new ParsedHint(hintName);

            // 4. 如果存在参数字符串，则进一步解析
            if (argsString != null) {
                // 按逗号分割参数
                String[] args = argsString.split("[,\\s]+");
                for (String arg : args) {
                    // 去除每个参数两边的空格后添加
                    hint.addArgument(arg.trim());
                }
            }
            parsedHints.add(hint);
        }
        return parsedHints;
    }

    public static void main(String[] args) {
        // 覆盖所有用例的测试字符串列表
        List<String> testCases = List.of(
                "/*+ PARALLEL(t1, 16) */",
                "/*+ PARALLEL(8) */",
                "/*+ PARALLEL(e, DEFAULT) */",
                "/*+ PARALLEL(e, AUTO) */",
                "/*+ NOPARALLEL(e) */",
                "/*+ parallel(t 10) */",
                "/*+ PARALLEL_INDEX(e, \"emp_dept_idx\", 8) */",
                "/*+ LEADING(e) USE_NL(d) PARALLEL(e, 8) */" // 包含多个Hint的复杂情况
        );

        for (String testCase : testCases) {
            System.out.println("------------------------------------");
            System.out.println("正在解析: " + testCase);
            List<ParsedHint> hints = parse(testCase);
            if (hints.isEmpty()) {
                System.out.println("  -> 解析失败或无内容。");
            } else {
                hints.forEach(h -> System.out.println("  -> " + h));
            }
        }
    }
}
