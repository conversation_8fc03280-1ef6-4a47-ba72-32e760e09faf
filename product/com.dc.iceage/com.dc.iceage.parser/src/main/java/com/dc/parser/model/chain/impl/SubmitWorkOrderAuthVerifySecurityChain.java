package com.dc.parser.model.chain.impl;

import com.dc.parser.component.ParserMapper;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class SubmitWorkOrderAuthVerifySecurityChain extends SqlAuthVerifySecurityChain {

    public SubmitWorkOrderAuthVerifySecurityChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {
        int workOrderType = parserParamDto.getWorkOrderType();
        List<String> verifyAuths = parserParamDto.getVerifyAuths();
        if (CollectionUtils.isEmpty(verifyAuths)) {
            webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
            return true;
        }

        switch (WorkOrderType.of(workOrderType)) {
            case RESULT_EXPORT: {
                boolean onlySelect = verifyAuths.size() == 1 && verifyAuths.get(0).equalsIgnoreCase(SqlConstant.KEY_SELECT);
                boolean onlyExport = verifyAuths.size() == 1 && verifyAuths.get(0).equalsIgnoreCase(SqlConstant.KEY_EXPORT);
                boolean both = verifyAuths.size() == 2 && verifyAuths.containsAll(List.of(SqlConstant.KEY_SELECT, SqlConstant.KEY_EXPORT));
                if (onlySelect) {
                    sqlParseModel.setSqlAuthModelList(
                            sqlParseModel.getSqlAuthModelList().stream()
                                    .filter(s -> SqlConstant.KEY_SELECT.equalsIgnoreCase(s.getOperation()))
                                    .collect(Collectors.toList())
                    );
                } else if (onlyExport) {
                    sqlParseModel.setSqlAuthModelList(
                            sqlParseModel.getSqlAuthModelList().stream().peek(s -> {
                                s.setOperation(SqlConstant.KEY_EXPORT);
                                s.setCustomExportOperation(true);
                            }).collect(Collectors.toList())
                    );
                } else if (both) {
                    ParserMapper parserMapper = Resource.getBeanRequireNonNull(ParserMapper.class);
                    List<SqlAuthModel> extraSqlAuthModels = new ArrayList<>();
                    sqlParseModel.getSqlAuthModelList().forEach(s -> {
                        if (!s.getOperation().equalsIgnoreCase(SqlConstant.KEY_EXPORT)) {
                            SqlAuthModel newSqlAuthModel = parserMapper.toSqlAuthModel(s);
                            newSqlAuthModel.setOperation(SqlConstant.KEY_EXPORT);
                            newSqlAuthModel.setCustomExportOperation(true);
                            extraSqlAuthModels.add(newSqlAuthModel);
                        }
                    });
                    sqlParseModel.setSqlAuthModelList(
                            sqlParseModel.getSqlAuthModelList().stream()
                                    .filter(s -> s.getOperation().equalsIgnoreCase(SqlConstant.KEY_SELECT) || s.getOperation().equalsIgnoreCase(SqlConstant.KEY_EXPORT))
                                    .collect(Collectors.toList())
                    );
                    sqlParseModel.getSqlAuthModelList().addAll(extraSqlAuthModels);
                } else {
                    webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                    return true;
                }
                break;
            }
            case DATA_IMPORT: {
                webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                return true;
            }
            default: {
                webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
                return true;
            }
        }

        //复用父类的逻辑
        //结果不需要接收
        super.proceed(webSQLParserResult);

        Set<String> box = new LinkedHashSet<>();
        sqlParseModel.getSqlAuthModelList().forEach(s -> {
            if (!s.isHasOperationAuth()) {
                if (s.getOperation().equalsIgnoreCase(SqlConstant.KEY_SELECT)) {
                    box.add("SELECT");
                    webSQLParserResult.setStatus(SqlExecuteStatus.SUBMIT_WORK_ORDER_FAIL.getValue());
                } else if (s.getOperation().equalsIgnoreCase(SqlConstant.KEY_EXPORT)) {
                    box.add("导出");
                    webSQLParserResult.setStatus(SqlExecuteStatus.SUBMIT_WORK_ORDER_FAIL.getValue());
                }
            }
        });
        String authToken = String.join("、", box);
        if (webSQLParserResult.getStatus().equals(SqlExecuteStatus.SUBMIT_WORK_ORDER_FAIL.getValue())) {
            webSQLParserResult.setMessage(getPromptMessage(authToken));
            webSQLParserResult.setUnvalidatedAuths(box);
        } else {
            webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
        }

        return true;
    }

    public enum WorkOrderType {
        RESULT_EXPORT(1),
        DATA_IMPORT(2),
        NONE(99);

        final int id;

        WorkOrderType(int i) {
            this.id = i;
        }

        public static WorkOrderType of(int id) {
            for (WorkOrderType each : values()) {
                if (each.id == id) {
                    return each;
                }
            }
            return NONE;
        }
    }

    public static String getPromptMessage(String authToken) {
        return "用户没有语句对应的" + authToken + "权限，无法提单";
    }
}
