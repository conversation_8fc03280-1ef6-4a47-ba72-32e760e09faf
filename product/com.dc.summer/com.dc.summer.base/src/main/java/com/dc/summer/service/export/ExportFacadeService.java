package com.dc.summer.service.export;

import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.chain.StreamChainRunner;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.data.transfer.registry.DataTransferProcessorDescriptor;
import com.dc.summer.data.transfer.registry.DataTransferRegistry;
import com.dc.summer.model.chain.impl.ToastChain;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.export.builder.ExportTaskBuilder;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.service.transfer.WebDataTransferName;
import com.dc.summer.service.transfer.request.ExportDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 导出门面服务
 * 提供统一的导出服务入口，封装复杂的导出流程
 */
@Slf4j
@Service
public class ExportFacadeService {

    @Autowired
    private ExportTaskBuilder taskBuilder;

    @Autowired
    private ResultSetService resultSetService;

    @Autowired
    private ResultService resultService;

    @Autowired
    private MessageService messageService;

    /**
     * 执行SQL导出
     * 
     * @param message SQL导出消息
     * @return 异步任务信息
     */
    public WebAsyncTaskInfo executeExport(SqlExportMessage message) {
        log.info("开始执行导出任务 - 用户: {}, 类型: {}", message.getUserId(), message.getExportType());
        
        try {
            // 1. 构建导出上下文
            ExportContext exportContext = taskBuilder.buildFromSqlExportMessage(message);
            
            // 2. 获取SQL上下文
            WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(exportContext.getToken(), exportContext.getTokenConfig());
            
            // 3. 创建异步任务
            return contextInfo.createAsyncTask("SQL Export", (taskId, monitor) -> {
                try {
                    log.info("执行导出任务: {}", exportContext.getTaskDescription());

                    // 1. 获取结果集数据
                    List<WebSQLQueryResultSet> resultSets = getResultSets(exportContext);

                    // 2. 执行导出
                    WebSQLTransferResult transferResult = executeTransfer(monitor, exportContext, resultSets);

                    // 3. 保存结果
                    saveExportResult(taskId, transferResult, contextInfo);

                    // 4. 发送告警消息
                    sendAlertMessageIfNeeded(exportContext, transferResult, contextInfo);

                    log.info("导出任务执行完成: {}", exportContext.getTaskDescription());

                } catch (Exception e) {
                    log.error("导出任务执行失败: {}", exportContext.getTaskDescription(), e);
                    throw e;
                }
            });
            
        } catch (Exception e) {
            log.error("创建导出任务失败", e);
            throw new RuntimeException("创建导出任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用的导出逻辑实现 - 使用建造器模式重构
     */
    protected WebSQLTransferResult executeTransfer(DBRProgressMonitor monitor, ExportContext exportContext, List<WebSQLQueryResultSet> resultSets) {
        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(exportContext.getToken());

        // 获取数据传输处理器
        DataTransferProcessorDescriptor processor = DataTransferRegistry.getInstance()
                .getProcessor(exportContext.getFormatConfig().getExportType().getProcessorFullId());

        // 构建文件名
        WebDataTransferName fileName = buildFileName(exportContext);

        // 使用建造器模式构建导出请求
        ExportDataRequest request = ExportDataRequest.builder(exportContext)
                .monitor(monitor)
                .processor(processor)
                .resultSets(resultSets)
                .streamFunction(resultSet-> ChainBuilder.build(new StreamChainRunner<WebSQLTransferResult>())
                        .addChain(new ToastChain(resultSet)))
                .transferZipName(new WebDataTransferName.ResultSetName())
                .transferFileName(fileName)
                .needToZipAndUpload(true)
                .isSingle(true)
                .deleteResultInfo(false)
                .customFileName(exportContext.getFormatConfig().getExportFileName())
                .originType(OriginType.BROWSER)
                .build();

        // 执行数据传输
        return contextInfo.getTransfer().exportDataByContext(request);
    }

    /**
     * 构建文件名
     */
    protected WebDataTransferName buildFileName(ExportContext context) {
        String customFileName = context.getFormatConfig().getExportFileName();
        if (StringUtils.isNotBlank(customFileName)) {
            return new WebDataTransferName.CustomResultSetName(customFileName);
        } else {
            return new WebDataTransferName.ResultSetName();
        }
    }

    /**
     * 获取结果集数据
     */
    private List<WebSQLQueryResultSet> getResultSets(ExportContext context) {
        // 首先尝试从缓存获取结果集
        Optional<List<WebSQLQueryResultSet>> optionalResultSets = resultSetService.getResultSetsOptional(context);
        
        if (optionalResultSets.isPresent()) {
            log.debug("从缓存获取结果集成功，数量: {}", optionalResultSets.get().size());
            return optionalResultSets.get();
        }
        
        // 如果缓存失效，返回空列表，让WebDataTransfer处理重新查询
        log.info("缓存失效，返回空结果集列表以便重新查询");
        return Collections.emptyList();
    }

    /**
     * 保存导出结果
     */
    private void saveExportResult(String taskId, WebSQLTransferResult transferResult, WebSQLContextInfo contextInfo) {
        try {
            WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo();
            executeInfo.setTransferResult(transferResult);
            resultService.saveStringValue(taskId, executeInfo, contextInfo.getExpirationTime());
            log.debug("导出结果保存成功，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("保存导出结果失败，任务ID: {}", taskId, e);
            throw new RuntimeException("保存导出结果失败", e);
        }
    }

    /**
     * 发送告警消息
     */
    private void sendAlertMessageIfNeeded(ExportContext exportContext, WebSQLTransferResult transferResult,
                                        WebSQLContextInfo contextInfo) {

        if (ConnectionPatternType.of(exportContext.getExecuteEvent().getConnectionPattern()).existsSecurityCollaboration()) {
            
            try {
                int securityRuleSetId = contextInfo.getConnection().getSecurityRuleSetId();
                WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo();
                executeInfo.setTransferResult(transferResult);
                
                messageService.sendAlertMessage(exportContext.getExecuteEvent(), executeInfo, securityRuleSetId);
                
                log.info("安全告警消息发送成功");
            } catch (Exception e) {
                log.error("发送安全告警消息失败", e);
            }
        }
    }

    /**
     * 将ExportContext转换为SqlExportMessage（临时兼容方法）
     * TODO: 后续可以重构MessageService以直接支持ExportContext
     */
    private SqlExportMessage convertContextToMessage(ExportContext context) {
        SqlExportMessage message = new SqlExportMessage();
        message.setToken(context.getToken());
        message.setUserId(context.getUserId());
        message.setTokenConfig(context.getTokenConfig());
        message.setExecuteEvent(context.getExecuteEvent());
        // 设置其他必要字段...
        return message;
    }
}
