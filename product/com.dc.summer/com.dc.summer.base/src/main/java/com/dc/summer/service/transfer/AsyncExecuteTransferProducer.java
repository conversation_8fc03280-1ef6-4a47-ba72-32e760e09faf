package com.dc.summer.service.transfer;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.data.transfer.IDataTransferConsumer;
import com.dc.summer.data.transfer.IDataTransferProcessor;
import com.dc.summer.data.transfer.database.DatabaseProducerSettings;
import com.dc.summer.data.transfer.database.DatabaseTransferProducer;
import com.dc.summer.data.transfer.internal.DTMessages;
import com.dc.summer.model.DBPContextProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.model.task.DBTaskUtils;
import com.dc.summer.service.data.AsyncExecuteStatistics;
import lombok.Getter;

import java.lang.reflect.InvocationTargetException;

public class AsyncExecuteTransferProducer extends DatabaseTransferProducer {

    private static final Log log = Log.getLog(AsyncExecuteTransferProducer.class);

    @Getter
    private final AsyncExecuteStatistics producerStatistics = new AsyncExecuteStatistics();

    public AsyncExecuteTransferProducer(@NotNull DBSDataContainer dataContainer, @Nullable DBDDataFilter dataFilter) {
        super(dataContainer, dataFilter);
    }

    @Override
    public DBCStatistics getStatistics() {
        return producerStatistics;
    }

    @Override
    public void transferData(DBRProgressMonitor monitor, IDataTransferConsumer consumer, IDataTransferProcessor processor, DatabaseProducerSettings settings, DBTTask task, Runnable recoverBefore, Runnable recoverAfter) throws DBException {
        String contextTask = DTMessages.data_transfer_wizard_job_task_export;

        DBSDataContainer databaseObject = getDatabaseObject();
        if (databaseObject == null) {
            throw new DBException("No input database object found");
        }
        DBPDataSource dataSource = databaseObject.getDataSource();
        assert (dataSource != null);

        long readFlags = DBSDataContainer.FLAG_NONE;
        if (settings.isSelectedColumnsOnly()) {
            readFlags |= DBSDataContainer.FLAG_USE_SELECTED_COLUMNS;
        }
        if (settings.isSelectedRowsOnly()) {
            readFlags |= DBSDataContainer.FLAG_USE_SELECTED_ROWS;
        }

        boolean newConnection = settings.isOpenNewConnections() && !getDatabaseObject().getDataSource().getContainer().getDriver().isEmbedded();
        boolean forceDataReadTransactions = Boolean.TRUE.equals(dataSource.getDataSourceFeature(DBPDataSource.FEATURE_LOB_REQUIRE_TRANSACTIONS));
        boolean selectiveExportFromUI = settings.isSelectedColumnsOnly() || settings.isSelectedRowsOnly();

        DBCExecutionContext context;
        if (dataContainer instanceof DBPContextProvider) {
            context = ((DBPContextProvider) dataContainer).getExecutionContext();
        } else {
            context = DBUtils.getDefaultContext(dataContainer, false);
        }
        if (context == null) {
            throw new DBCException("Can't retrieve execution context from data container " + dataContainer);
        }
        if (!selectiveExportFromUI && newConnection) {
            context = DBUtils.getObjectOwnerInstance(getDatabaseObject()).openIsolatedContext(monitor, "Data transfer producer", context, true, null);
            DBExecUtils.setExecutionContextDefaults(monitor, dataSource, context, defaultCatalog, null, defaultSchema);
        }
        if (task != null) {
            DBTaskUtils.initFromContext(monitor, task, context);
        }

        long finalReadFlags = readFlags;
        DBExecUtils.tryExecuteRecover(context, dataSource, param -> {

            try (DBCSession session = param.openSession(monitor, DBCExecutionPurpose.USER, contextTask)) {

                Boolean oldAutoCommit = null;
                DBCSavepoint savepoint = null;
                try {
                    AbstractExecutionSource transferSource = new AbstractExecutionSource(dataContainer, param, consumer);
                    session.enableLogging(false);
                    if (!selectiveExportFromUI && (newConnection || forceDataReadTransactions)) {
                        // Turn off auto-commit in source DB
                        // Auto-commit has to be turned off because some drivers allows to read LOBs and
                        // other complex structures only in transactional mode
                        try {
                            DBCTransactionManager txnManager = DBUtils.getTransactionManager(param);
                            if (txnManager != null && txnManager.isSupportsTransactions()) {
                                oldAutoCommit = txnManager.isAutoCommit();
                                txnManager.setAutoCommit(monitor, false);
                                if (txnManager.supportsSavepoints()) {
                                    savepoint = txnManager.setSavepoint(monitor, "Data transfer start");
                                }
                            }
                        } catch (DBCException e) {
                            log.warn("Can't change auto-commit", e);
                        }

                    }
                    long totalRows = 0;
                    if (settings.isQueryRowCount() && dataContainer.isFeatureSupported(DBSDataContainer.FEATURE_DATA_COUNT)) {
                        monitor.beginTask(DTMessages.data_transfer_wizard_job_task_retrieve, 1);
                        try {
                            totalRows = dataContainer.countData(transferSource, session, dataFilter, finalReadFlags);
                        } catch (Throwable e) {
                            log.warn("Can't retrieve row count from '" + dataContainer.getName() + "'", e);
                            try {
                                DBCTransactionManager txnManager = DBUtils.getTransactionManager(session.getExecutionContext());
                                if (txnManager != null && !txnManager.isAutoCommit()) {
                                    txnManager.rollback();
                                }
                            } catch (Throwable e1) {
                                log.warn("Error rolling back transaction", e1);
                            }
                        } finally {
                            monitor.done();
                        }
                    }

                    monitor.beginTask(DTMessages.data_transfer_wizard_job_task_export_table_data, (int) totalRows);

                    try {
                        monitor.subTask("Read data");

                        // Perform export
                        if (settings.getExtractType() == DatabaseProducerSettings.ExtractType.SINGLE_QUERY) {
                            // Just do it in single query
                            //TODO
                            DBCStatistics stat = dataContainer.readData(transferSource, session, consumer, dataFilter, settings.getOffset(), settings.getExportLimit(), finalReadFlags, settings.getFetchSize(), settings.getData());
                            AsyncExecuteStatistics asyncStat = (AsyncExecuteStatistics) stat;
                            producerStatistics.setWebSQLQueryResult(asyncStat.getWebSQLQueryResult());
                            producerStatistics.setQueryText(asyncStat.getQueryText());
                            producerStatistics.accumulate(asyncStat);
                            producerStatistics.setError(asyncStat.getError());
                        } else {
                            // Read all data by segments
                            long offset = 0;
                            long segmentSize = settings.getSegmentSize();
                            for (; ; ) {
                                DBCStatistics statistics = dataContainer.readData(
                                        transferSource, session, consumer, dataFilter, offset, segmentSize, finalReadFlags, settings.getFetchSize(), settings.getData());
                                if (statistics == null || statistics.getRowsFetched() < segmentSize) {
                                    // Done
                                    break;
                                }
                                producerStatistics.accumulate(statistics);
                                offset += statistics.getRowsFetched();
                            }
                        }
                    } finally {
                        monitor.done();
                    }

                } catch (DBException e) {
                    throw new InvocationTargetException(e);
                } finally {
                    try {
                        if (!selectiveExportFromUI && (newConnection || forceDataReadTransactions)) {
                            DBCTransactionManager txnManager = DBUtils.getTransactionManager(param);
                            if (txnManager != null && txnManager.isSupportsTransactions()) {
                                if (!txnManager.isAutoCommit()) {
                                    txnManager.rollback();
                                }
                                if (savepoint != null) {
                                    txnManager.releaseSavepoint(monitor, savepoint);
                                }
                                if (oldAutoCommit != null) {
                                    txnManager.setAutoCommit(monitor, oldAutoCommit);
                                }
                            }
                        }
                        if (!selectiveExportFromUI && newConnection) {
                            param.close();
                        }
                    } catch (DBException dbe) {
                        log.error("transfer data finally error.", dbe);
                    }
                }
            }
        }, recoverBefore, recoverAfter);
    }
}
