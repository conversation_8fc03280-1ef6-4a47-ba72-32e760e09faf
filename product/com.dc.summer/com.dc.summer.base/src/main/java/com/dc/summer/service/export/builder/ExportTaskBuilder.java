package com.dc.summer.service.export.builder;

import com.dc.springboot.core.model.execution.JobExportMessage;
import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.type.TextIdentifierType;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.summer.model.data.model.ResultModel;
import com.dc.summer.model.data.model.ResultsIndexModel;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.export.config.ExcelConfig;
import com.dc.summer.model.export.config.ExportFormatConfig;
import com.dc.summer.model.export.config.SecurityConfig;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.model.type.LineDelimiterType;
import com.dc.summer.model.type.PageSelectedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 导出任务建造者
 * 使用建造者模式简化复杂的导出上下文构建
 */
@Slf4j
@Component
public class ExportTaskBuilder {

    /**
     * 从SqlExportMessage构建ExportContext
     * 
     * @param message SQL导出消息
     * @return 导出上下文
     */
    public ExportContext buildFromSqlExportMessage(SqlExportMessage message) {
        log.debug("从SqlExportMessage构建导出上下文");

        int stage = 0;

        if (message.getPageSelected() == PageSelectedType.CURRENT_PAGE) {
            if (CollectionUtils.size(message.getSqlExportModels()) == 1) {
                SqlExportModel sqlExportModel = message.getSqlExportModels().get(0);
                if (CollectionUtils.size(sqlExportModel.getResultsIndexModels()) == 1) {
                    List<ResultModel> resultModels = sqlExportModel.getResultsIndexModels().get(0).getResultModels();
                    if (CollectionUtils.size(resultModels) == 1) {
                        stage = resultModels.get(0).getResultIndex();
                    }
                }
            }
        }

//        if (CollectionUtils.size(message.getSqlExportModels()) == 1) {
//            SqlExportModel sqlExportModel = message.getSqlExportModels().get(0);
//            if (CollectionUtils.size(sqlExportModel.getResultsIndexModels()) == 1) {
//                List<ResultModel> resultModels = sqlExportModel.getResultsIndexModels().get(0).getResultModels();
//                if (CollectionUtils.size(resultModels) == 1) {
//                    stage = resultModels.get(0).getResultIndex();
//                }
//            }
//        }

        return ExportContext.builder()
                .token(message.getToken())
                .userId(message.getUserId())
                .tokenConfig(message.getTokenConfig())
                .formatConfig(buildFormatConfig(message))
                .excelConfig(buildExcelConfig(message))
                .securityConfig(buildSecurityConfig(message))
                .sqlExportModels(message.getSqlExportModels())
                .resultFormat(message.getResultFormat())
                .stage(stage)
                .executeEvent(message.getExecuteEvent())
                .build();
    }

    /**
     * 从JobExportMessage构建ExportContext
     * 
     * @param message 任务导出消息
     * @return 导出上下文
     */
    public ExportContext buildFromJobExportMessage(JobExportMessage message) {
        log.debug("从JobExportMessage构建导出上下文");
        
        // 先转换为SqlExportMessage，然后构建上下文
        SqlExportMessage sqlExportMessage = SqlExportMessage.buildInstance(message);
        return buildFromSqlExportMessage(sqlExportMessage);
    }

    /**
     * 创建新的建造者实例
     * 
     * @return 建造者实例
     */
    public static ExportContextBuilder newBuilder() {
        return new ExportContextBuilder();
    }

    /**
     * 构建格式配置
     */
    private ExportFormatConfig buildFormatConfig(SqlExportMessage message) {
        ExportFormatConfig config = ExportFormatConfig.builder()
                .exportType(message.getExportType())
                .fileCharset(message.getFileCharset())
                .textIdentifier(message.getTextIdentifier())
                .lineDelimiter(message.getLineDelimiter())
                .columnDelimiter(message.getColumnDelimiter())
                .otherDelimiter(message.getOtherDelimiter())
                .pageSelected(message.getPageSelected())
                .exportFileName(message.getExportFileName())
                .splitFileSize(message.getSplitFileSize())
                .build();
        
        // 设置默认的列分隔符
        config.setDefaultColumnDelimiter();
        
        return config;
    }

    /**
     * 构建Excel配置
     */
    private ExcelConfig buildExcelConfig(SqlExportMessage message) {
        return ExcelConfig.builder()
                .useOriginalFormat(message.getExcelUseOriginalFormat())
                .useNumberFormat(message.isExcelUseNumberFormat())
                .datetimeFormat(message.getExcelDatetimeFormat())
                .build();
    }

    /**
     * 构建安全配置
     */
    private SecurityConfig buildSecurityConfig(SqlExportMessage message) {
        return SecurityConfig.builder()
                .watermarkContent(message.getWatermarkContent())
                .watermarkAngle(message.getWatermarkAngle())
                .encryptPassword(message.getEncryptPassword())
                .exportDesensitize(message.isExportDesensitize())
                .build();
    }

    /**
     * 导出上下文建造者
     * 提供链式调用的方式构建导出上下文
     */
    public static class ExportContextBuilder {
        private final ExportContext.ExportContextBuilder contextBuilder = ExportContext.builder();
        private final ExportFormatConfig.ExportFormatConfigBuilder formatBuilder = ExportFormatConfig.builder();
        private final ExcelConfig.ExcelConfigBuilder excelBuilder = ExcelConfig.builder();
        private final SecurityConfig.SecurityConfigBuilder securityBuilder = SecurityConfig.builder();

        public ExportContextBuilder token(String token) {
            contextBuilder.token(token);
            return this;
        }

        public ExportContextBuilder userId(String userId) {
            contextBuilder.userId(userId);
            return this;
        }

        public ExportContextBuilder exportType(ExportType exportType) {
            formatBuilder.exportType(exportType);
            return this;
        }

        public ExportContextBuilder fileCharset(String charset) {
            formatBuilder.fileCharset(charset);
            return this;
        }

        public ExportContextBuilder textIdentifier(TextIdentifierType textIdentifier) {
            formatBuilder.textIdentifier(textIdentifier);
            return this;
        }

        public ExportContextBuilder lineDelimiter(LineDelimiterType lineDelimiter) {
            formatBuilder.lineDelimiter(lineDelimiter);
            return this;
        }

        public ExportContextBuilder columnDelimiter(ColumnDelimiterType columnDelimiter) {
            formatBuilder.columnDelimiter(columnDelimiter);
            return this;
        }

        public ExportContextBuilder pageSelected(PageSelectedType pageSelected) {
            formatBuilder.pageSelected(pageSelected);
            return this;
        }

        public ExportContextBuilder exportFileName(String fileName) {
            formatBuilder.exportFileName(fileName);
            return this;
        }

        public ExportContextBuilder excelUseOriginalFormat(int useOriginalFormat) {
            excelBuilder.useOriginalFormat(useOriginalFormat);
            return this;
        }

        public ExportContextBuilder excelUseNumberFormat(boolean useNumberFormat) {
            excelBuilder.useNumberFormat(useNumberFormat);
            return this;
        }

        public ExportContextBuilder excelDatetimeFormat(String datetimeFormat) {
            excelBuilder.datetimeFormat(datetimeFormat);
            return this;
        }

        public ExportContextBuilder watermark(String content, Integer angle) {
            securityBuilder.watermarkContent(content).watermarkAngle(angle);
            return this;
        }

        public ExportContextBuilder encryption(String password) {
            securityBuilder.encryptPassword(password);
            return this;
        }

        public ExportContextBuilder desensitize(boolean desensitize) {
            securityBuilder.exportDesensitize(desensitize);
            return this;
        }

        public ExportContext build() {
            ExportFormatConfig formatConfig = formatBuilder.build();
            formatConfig.setDefaultColumnDelimiter();
            
            return contextBuilder
                    .formatConfig(formatConfig)
                    .excelConfig(excelBuilder.build())
                    .securityConfig(securityBuilder.build())
                    .build();
        }
    }
}
