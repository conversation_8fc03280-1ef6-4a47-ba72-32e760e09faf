package com.dc.repository.mysql.model;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_async_query")
public class DcAsyncQuery {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("token")
    private String token;

    @TableField("user_id")
    private String userId;

    @TableField("connect_id")
    private String connectId;

    @TableField("schema_id")
    private String schemaId;

    @TableField("schema_name")
    private String schemaName;

    @TableField("connection_desc")
    private String connectionDesc;

    @TableField("instance_name")
    private String instanceName;

    @TableField("environment")
    private Integer environment;

    @TableField("db_type")
    private Integer dbType;

    @TableField("execute_sql")
    private String executeSql;

    @TableField("show_sql")
    private String showSql;

    @TableField("execute_log")
    private String executeLog;

    @TableField("execute_status")
    private Integer executeStatus;

    @TableField("mask_method")
    private Integer maskMethod;

    @TableField("execute_result")
    private String executeResult;

    @TableField("execute_metadata")
    private String executeMetadata;

    @TableField("execute_time")
    private Date executeTime;

    @TableField("expiration_time")
    private Date expirationTime;

    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;

    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @TableField(value = "download_result")
    private String downloadResult;

}
