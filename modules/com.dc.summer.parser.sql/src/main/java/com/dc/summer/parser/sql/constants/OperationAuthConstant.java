package com.dc.summer.parser.sql.constants;

import java.util.List;

public class OperationAuthConstant {

    public static final String select = "select";
    public static final String statistics = "statistics";
    public static final String insert = "insert";
    public static final String update = "update";
    public static final String drop = "drop";
    public static final String truncate = "truncate";
    public static final String delete = "delete";
    public static final String create = "create";
    public static final String alter = "alter";
    public static final String vacuum = "vacuum";
    public static final String export = "export";

    public static final String leader_role = "leader_role";
    public static final String dba_role = "dba_role";
    public static final String buss_role = "buss_role";
    public static final String dba_sys_role = "dba_sys_role";
    public static final String buss_sys_role = "buss_sys_role";

    public static final String call = "call";
    public static final String exec = "exec";
    public static final String desc = "desc";
    public static final String show = "show";

    public static final String submit = "submit";
    public static final String cancel = "cancel";

    public static final String grant = "grant";
    public static final String revoke = "revoke";

    public static final String create_function = "create_function";
    public static final String create_index = "create_index";
    public static final String create_procedure = "create_procedure";
    public static final String create_table = "create_table";
    public static final String create_trigger = "create_trigger";
    public static final String create_view = "create_view";
    public static final String create_database = "create_database";
    public static final String create_schema = "create_schema";
    public static final String create_user = "create_user";
    public static final String create_role = "create_role";

    public static final String drop_function = "drop_function";
    public static final String drop_index = "drop_index";
    public static final String drop_procedure = "drop_procedure";
    public static final String drop_table = "drop_table";
    public static final String drop_trigger = "drop_trigger";
    public static final String drop_view = "drop_view";

    public static final String alter_function = "alter_function";
    public static final String alter_index = "alter_index";
    public static final String alter_procedure = "alter_procedure";
    public static final String alter_table = "alter_table";
    public static final String alter_trigger = "alter_trigger";
    public static final String alter_view = "alter_view";

    public static final String[] instance_auth =
            {
                    select, statistics, insert, update, drop, truncate, delete, create, alter, export,
                    dba_role, buss_role, dba_sys_role, buss_sys_role,
                    desc, show, call, submit, cancel, grant, revoke,
                    create_function, create_index, create_procedure, create_table, create_trigger, create_view,
                    drop_function, drop_index, drop_procedure, drop_table, drop_trigger, drop_view,
                    alter_function, alter_index, alter_procedure, alter_table, alter_trigger, alter_view, vacuum
            };
    public static final String[] schema_auth =
            {
                    select, statistics, insert, update, drop, truncate, delete, create, alter, export,
                    desc, show, call, submit, cancel,
                    create_function, create_index, create_procedure, create_table, create_trigger, create_view,
                    drop_function, drop_index, drop_procedure, drop_table, drop_trigger, drop_view,
                    alter_function, alter_index, alter_procedure, alter_table, alter_trigger, alter_view, vacuum
            };
    public static final String[] table_auth =
            {
                    select, statistics, insert, update, drop, truncate, delete, export,
                    desc, drop_table, vacuum
            };
    public static final String[] table_group_auth =
            {
                    select, statistics, insert, update, drop, delete, export,
                    create, alter
            };
    public static final String[] object_auth =
            {
                    select, statistics, call, export
            };
    public static final String[] ddl_subdivide_auth =
            {
                    create_function, create_index, create_procedure, create_table, create_trigger, create_view, create_database, create_schema,
                    create_user, create_role,
                    drop_function, drop_index, drop_procedure, drop_table, drop_trigger, drop_view,
                    alter_function, alter_index, alter_procedure, alter_table, alter_trigger, alter_view
            };

    public static final List<String> authsForTableCreator = List.of(select, statistics, update, delete, truncate, insert, drop_table, drop, alter_table, alter);

}
