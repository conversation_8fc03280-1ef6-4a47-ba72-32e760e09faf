

package com.dc.summer.ext.mysql.model;

import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.utils.NewJdbcUtil;
import com.dc.utils.BitTypeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * MySQLDataSourceInfo
 */
public class MySQLDataSourceInfo extends JDBCDataSourceInfo {

    public MySQLDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean supportsMultipleResults() {
        return true;
    }

    @Override
    public boolean needsTableMetaForColumnResolution() {
        return true;
    }

    @Override
    public String getFormatColumnName(String column) {
        return String.format("`%s`", column);
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return true;
    }


    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "select column_name from information_schema.key_column_usage " +
                "where constraint_schema = '" + schemaName + "' and  table_name = '" + tableName + "'  and constraint_name='PRIMARY'";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("COLUMN_NAME") != null) {
                columns.add(map.get("COLUMN_NAME").toString());
            } else if (map.get("column_name") != null) {
                columns.add(map.get("column_name").toString());
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        return "select table_name from information_schema.tables " +
                "WHERE table_type in ('BASE TABLE','SYSTEM VIEW') and upper(table_schema)=upper('" + schemaName + "') and upper(table_name)=upper('" + tableName + "')";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("table_name") != null) {
                realName = map.get("table_name").toString();
                break;
            } else if (map.get("TABLE_NAME") != null) {
                realName = map.get("TABLE_NAME").toString();
                break;
            }
        }

        return realName;
    }

    @Override
    public List<String> getBigDataColumnType() {
        return Arrays.asList("BLOB", "TINYBLOB", "MEDIUMBLOB", "MEDIUMTEXT", "LONGBLOB", "LONGTEXT", "ENUM", "SET");
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return "SELECT a.ordinal_position AS ordinal_position, a.table_name AS table_name, a.column_name AS column_name, a.data_type AS data_type, a.column_type AS column_type " +
                " , CASE  " +
                "  WHEN a.is_nullable = 'YES' THEN 1 " +
                "  ELSE 0 " +
                " END AS nullable, a.column_default AS data_default, a.column_comment AS comments, a.column_key AS column_key " +
                " , CASE  " +
                "  WHEN a.column_key = 'PRI' THEN 1 " +
                "  ELSE 0 " +
                " END AS is_primary_key " +
                " , CASE  " +
                "  WHEN extra = 'auto_increment' THEN 1 " +
                "  ELSE 0 " +
                " END AS is_increment, a.CHARACTER_MAXIMUM_LENGTH AS data_length " +
                "FROM information_schema.COLUMNS a " +
                "WHERE a.table_schema = '" + schemaName + "' " +
                " AND a.Table_Name = '" + tableName + "' " +
                "ORDER BY ordinal_position ASC";
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("VARCHAR", "NVARCHAR", "CHAR", "LONGTEXT", "TEXT", "JSON").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("\\") ? item.getFieldValue().toString().replace("\\\\", "\\\\\\\\") : item.getFieldValue().toString();
                    varcharData = varcharData.contains("\"") ? varcharData.replace("\"", "\\\"") : varcharData;
                    varcharData = varcharData.contains("'") ? varcharData.replace("'", "''") : varcharData;
                    data.add(String.format("'%s'", varcharData));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && "BIT".equals(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String value;
                    String fieldValue = item.getFieldValue().toString();

                    if (BitTypeUtils.isValidPattern(fieldValue)) {
                        value = fieldValue;
                    } else {
                        value = String.format("b'%s'", fieldValue);
                    }
                    data.add(value);
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("BINARY", "VARBINARY").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String hex = NewJdbcUtil.bytes2HexString(NewJdbcUtil.string2Bytes(item.getFieldValue().toString()));
                    data.add(String.format("0x%s", hex));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("GEOMETRY", "GEOMETRYCOLLECTION", "MULTIPOINT", "MULTIPOLYGON", "POLYGON", "LINESTRING", "MULTILINESTRING", "POINT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    data.add(String.format("ST_GeomFromText('%s')", item.getFieldValue()));
                } else {
                    String varcharData = item.getFieldValue().toString().contains("\"") ? item.getFieldValue().toString().replaceAll("\"", "\\\"") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "`,`");
        String values = StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO `%s`.`%s` (%s) VALUES (%s)", schemaName, tableName, "`" + columns + "`", values);
        }
        return String.format("INSERT INTO `%s` (%s) VALUES (%s)", tableName, "`" + columns + "`", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        return generateInsertOrIgnoreOrReplaceSqlBatch(schemaName, tableName, list, false, false);
    }

    @Override
    public String generateInsertIgnoreSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        return generateInsertOrIgnoreOrReplaceSqlBatch(schemaName, tableName, list, true, false);
    }

    @Override
    public String generateReplaceSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        return generateInsertOrIgnoreOrReplaceSqlBatch(schemaName, tableName, list, false, true);
    }

    /**
     * 用于生成insert [ignore] statement 或者 replace statement<br>
     * <a href="https://dev.mysql.com/doc/refman/5.7/en/insert.html">mysql insert statement 官方文档</a><br>
     * <a href="https://dev.mysql.com/doc/refman/5.7/en/replace.html">mysql replace statement 官方文档</a>
     */
    public String generateInsertOrIgnoreOrReplaceSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, boolean ignore, boolean replace) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("`,`"));

        String values = list.stream()
                .map(sqlFieldDataList -> sqlFieldDataList.stream()
                .map(item -> {
                    if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                        if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("VARCHAR", "NVARCHAR", "CHAR", "LONGTEXT", "TEXT", "JSON").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                            String varcharData = item.getFieldValue().toString().contains("\\") ? item.getFieldValue().toString().replace("\\\\", "\\\\\\\\") : item.getFieldValue().toString();
                            varcharData = varcharData.contains("\"") ? varcharData.replace("\"", "\\\"") : varcharData;
                            varcharData = varcharData.contains("'") ? varcharData.replace("'", "''") : varcharData;
                            return String.format("'%s'", varcharData);
                        } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && "BIT".equals(item.getFieldType().toUpperCase(Locale.ROOT))) {
                            String value;
                            String fieldValue = item.getFieldValue().toString();

                            if (BitTypeUtils.isValidPattern(fieldValue)) {
                                value = fieldValue;
                            } else {
                                value = String.format("b'%s'", fieldValue);
                            }
                            return value;
                        } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("BINARY", "VARBINARY").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                            String hex = NewJdbcUtil.bytes2HexString(NewJdbcUtil.string2Bytes(item.getFieldValue().toString()));
                            return String.format("0x%s", hex);
                        } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("GEOMETRY", "GEOMETRYCOLLECTION", "MULTIPOINT", "MULTIPOLYGON", "POLYGON", "LINESTRING", "MULTILINESTRING", "POINT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                            return String.format("ST_GeomFromText('%s')", item.getFieldValue());
                        } else {
                            String varcharData = item.getFieldValue().toString().contains("\"") ? item.getFieldValue().toString().replaceAll("\"", "\\\"") : item.getFieldValue().toString();
                            return String.format("'%s'", varcharData);
                        }
                    } else {
                        return String.format("%s", "NULL");
                    }
                })
                .collect(Collectors.joining(",", "(", ")")))
                .collect(Collectors.joining(","));

        if (null != schemaName) {
            return String.format("%s %s INTO `%s`.`%s` (%s) VALUES %s", replace ? "REPLACE" : "INSERT", ignore ? "IGNORE" : "",  schemaName, tableName, "`" + fields + "`", values);
        }
        return String.format("%s %s INTO `%s` (%s) VALUES %s", replace ? "REPLACE" : "INSERT", ignore ? "IGNORE" : "", tableName, "`" + fields + "`", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {

        if (StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE `%s`.`%s`", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE `%s`", tableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select SCHEMA_NAME,DEFAULT_CHARACTER_SET_NAME from information_schema.schemata");

        String countTableSql = "select COUNT(1) AS count from information_schema.tables WHERE upper(table_type) in ('BASE TABLE', 'SYSTEM VIEW') and TABLE_SCHEMA = '%s';";

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_mysql_db_schema");
            returnMap.put("username", map.get("SCHEMA_NAME"));
            returnMap.put("charset", map.get("DEFAULT_CHARACTER_SET_NAME") != null ? map.get("DEFAULT_CHARACTER_SET_NAME") : "");

            if (Arrays.asList("information_schema", "mysql", "performance_schema", "sys")
                    .contains(((String) map.get("SCHEMA_NAME")).toLowerCase())) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", 0L);

            List<Map<String, Object>> result = DBExecUtils.executeQuery(monitor, context, "get schema count", String.format(countTableSql, map.get("SCHEMA_NAME")));
            if (!result.isEmpty()) {
                returnMap.put("count", Long.parseLong(result.get(0).get("count").toString()));
            }

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (List.of("DOUBLE", "FLOAT", "FLOAT UNSIGNED").contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            return false;
        }
        return super.showInteger(column);
    }

    @Override
    public boolean supportsTableColumnSQL() {
        return true;
    }
}
