package com.dc.parser.model.chain.impl;

import com.dc.parser.model.SchemaAccessibleTime;
import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.repository.mysql.mapper.AccessibleTimeMapper;
import com.dc.repository.mysql.model.AccessibleRule;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.parser.constants.DCConstants;
import com.dc.parser.model.AccessControlModel;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.repository.mysql.model.AccessibleTime;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.parser.util.ExecuteSqlUtil;
import com.dc.summer.parser.utils.SqlPreparedUtil;
import com.dc.repository.redis.service.RedisService;
import com.dc.type.DatabaseType;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
public class SqlAccessControlChain extends SqlCheckParserChain {

    private final AccessibleTimeMapper accessibleTimeMapper = Resource.getBeanRequireNonNull(AccessibleTimeMapper.class);

    private final RedisService redisService = Resource.getBeanRequireNonNull("redisService", RedisService.class);

    private final String userIp;

    private static final String ANY_IPADDRESS_CIDR = "0.0.0.0/0";

    public SqlAccessControlChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
        this.userIp = webSQLParserInfo.getUserIp();
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        try {

            // 白名单操作（不受访问管控的限制）
            boolean passAuth = SqlPreparedUtil.isPassAuth(sqlParseModel, parserParamDto.getDbType());
            if (passAuth) {
                webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                return true;
            }

            // 编译无效对象、会话窗口 操作时无需对“访问管控”的限制进行校验
            if (parserParamDto.getOrigin() != null) {
                if (OriginType.of(parserParamDto.getOrigin()) == OriginType.INVALID ||
                        OriginType.of(parserParamDto.getOrigin()) == OriginType.CONVERSATION) {
                    return true;
                }
            }

            // 一次查出三个维度(表、Schema、实例)的访问管控信息
            AccessibleTimeResult accessibleTimeResult = getAllAccessibleTimes(sqlParseModel.getSqlAuthModelList(), parserParamDto.getConnectId());

            Map<String, AccessibleTime> tableAccessibleTimes = new HashMap<>();
            Map<String, AccessibleTime> schemaAccessibleTimes = new HashMap<>();
            Map<String, AccessibleTime> instanceAccessibleTimes = new HashMap<>();

            if (accessibleTimeResult.getInstanceResult() != null) {
                List<AccessibleTime> instanceResult = accessibleTimeResult.getInstanceResult();
                if (!instanceResult.isEmpty()) {
                    instanceResult.stream().filter(accessibleTime -> CollectionUtils.isNotEmpty(accessibleTime.getAccessibleRules()))
                            .forEach(accessibleTime -> instanceAccessibleTimes.put(parserParamDto.getInstance().getInstance_name(), accessibleTime));
                }
            }

            if (accessibleTimeResult.getSchemaResult() != null) {
                List<AccessibleTime> schemaResult = accessibleTimeResult.getSchemaResult();
                if (!schemaResult.isEmpty()) {
                    schemaResult.stream().filter(accessibleTime -> CollectionUtils.isNotEmpty(accessibleTime.getAccessibleRules()))
                            .forEach(accessibleTime -> schemaAccessibleTimes.put(accessibleTime.getSchema_id(), accessibleTime));
                }
            }

            if (accessibleTimeResult.getTableResult() != null) {
                List<AccessibleTime> tableResult = accessibleTimeResult.getTableResult();
                if (!tableResult.isEmpty()) {
                    tableResult.stream().filter(accessibleTime -> CollectionUtils.isNotEmpty(accessibleTime.getAccessibleRules()))
                            .forEach(accessibleTime -> tableAccessibleTimes.put(accessibleTime.getSchema_id() + "." + accessibleTime.getObject_name(), accessibleTime));
                }
            }

            // 三种访问管控的级别
            List<AccessControlModel> tableAccessControl = new ArrayList<>();
            List<AccessControlModel> schemaAccessControl = new ArrayList<>();
            List<AccessControlModel> instanceAccessControl = new ArrayList<>();

            //移除自定义普通函数，自定义普通函数不能受访问管控影响
            List<SqlAuthModel> sqlAuthModels = sqlParseModel.getSqlAuthModelList().stream()
                    .filter(each -> !(each.getType().equalsIgnoreCase(SqlConstant.FUNCTION) && each.getOperation().equalsIgnoreCase(SqlConstant.KEY_CALL)))
                    .collect(Collectors.toList());

            for (SqlAuthModel sqlAuthModel : sqlAuthModels) {
                String schemaName = (StringUtils.isNotBlank(sqlAuthModel.getDblinkName()) ? sqlAuthModel.getDblinkName() + "." : "") + sqlAuthModel.getFullSchemaName();
                String schemaUniqueKey = sqlAuthModel.getSchemaUniqueKey();
                String name = sqlAuthModel.getRealObjectName();
                String operation = sqlAuthModel.getOperation();
                if (Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER).contains(operation)
                        && sqlAuthModel.getType() != null
                        && Arrays.asList(SqlConstant.KEY_USER, SqlConstant.KEY_DATABASE, SqlConstant.KEY_SCHEMA).contains(sqlAuthModel.getType().toUpperCase(Locale.ROOT))) {
                    operation = SqlConstant.KEY_DATABASE;
                }
                boolean hasControl = false;

                if (proveIsTable(sqlParseModel.getAction(), sqlAuthModel)) {
                    // 表级别访问管控
                    if (tableAccessibleTimes.get(schemaUniqueKey + "." + name) != null) {
                        String accessControlModelName = DatabaseType.ELASTIC_SEARCH.getValue().equals(parserParamDto.getDbType()) ? name : schemaName + "." + name;
                        tableAccessControl.add(ExecuteSqlUtil.buildAccessControlModel(accessControlModelName, operation, sqlAuthModel.getDdlSubdivideOperation(), tableAccessibleTimes.get(schemaUniqueKey + "." + name)));
                        hasControl = true;
                    }
                }

                if (!hasControl) {
                    // schema级别访问管控
                    if (schemaAccessibleTimes.get(schemaUniqueKey) != null) {
                        schemaAccessControl.add(ExecuteSqlUtil.buildAccessControlModel(schemaName, operation, sqlAuthModel.getDdlSubdivideOperation(), schemaAccessibleTimes.get(schemaUniqueKey)));
                        hasControl = true;
                    }
                }

                if (!hasControl) {
                    // 实例级别访问管控
                    if (instanceAccessibleTimes.get(parserParamDto.getInstance().getInstance_name()) != null) {
                        String instanceName = StringUtils.isNotBlank(sqlAuthModel.getDblinkName()) ? sqlAuthModel.getDblinkName() : parserParamDto.getInstance().getInstance_name();
                        instanceAccessControl.add(ExecuteSqlUtil.buildAccessControlModel(instanceName, operation, sqlAuthModel.getDdlSubdivideOperation(), instanceAccessibleTimes.get(parserParamDto.getInstance().getInstance_name())));
                    }
                }
            }

            // 解析不到具体对象的按照当前schema和实例拦截
            if (sqlParseModel.getSqlAuthModelList().isEmpty() && StringUtils.isNotBlank(parserParamDto.getSchemaName())) {
                // schema级别访问管控
                List<AccessibleTime> schemasAccessibleTime = accessibleTimeMapper.getSchemaAccessibleTimes(parserParamDto.getConnectId(), Collections.singleton(parserParamDto.getSchemaId()));
                if (schemasAccessibleTime != null && !schemasAccessibleTime.isEmpty()) {
                    String fullSchemaName = (StringUtils.isNotBlank(parserParamDto.getInstance().getDblink_name()) ? parserParamDto.getInstance().getDblink_name() + "." : "") +
                            (DatabaseType.getCatalogDatabaseIntegerValueList().contains(parserParamDto.getDbType()) ? parserParamDto.getCatalogName() + "." : "") + parserParamDto.getSchemaName();
                    schemaAccessControl.add(ExecuteSqlUtil.buildAccessControlModel(fullSchemaName, sqlParseModel.getOperation(), null, schemasAccessibleTime.get(0)));
                } else {
                    // 实例级别访问管控
                    List<AccessibleTime> accessibleTimeInstance = accessibleTimeMapper.getInstanceAccessibleTimes(parserParamDto.getConnectId());
                    if (CollectionUtils.isNotEmpty(accessibleTimeInstance)) {
                        String instanceName = StringUtils.isNotBlank(parserParamDto.getInstance().getDblink_name()) ? parserParamDto.getInstance().getDblink_name() : parserParamDto.getInstance().getInstance_name();
                        instanceAccessControl.add(ExecuteSqlUtil.buildAccessControlModel(instanceName, sqlParseModel.getOperation(), null, accessibleTimeInstance.get(0)));
                    }
                }
            }

            Set<ControlledObject> controlledObjects = null;
            if (!tableAccessControl.isEmpty()) {
                controlledObjects = getControlledObjects(tableAccessControl, parserParamDto, DCConstants.DC_TABLE, webSQLParserResult);
            }
            if (CollectionUtils.isEmpty(controlledObjects) && !schemaAccessControl.isEmpty()) {
                controlledObjects = getControlledObjects(schemaAccessControl, parserParamDto, DCConstants.DC_SCHEMA, webSQLParserResult);
            }
            if (CollectionUtils.isEmpty(controlledObjects) && !instanceAccessControl.isEmpty()) {
                controlledObjects = getControlledObjects(instanceAccessControl, parserParamDto, DCConstants.DC_INSTANCE, webSQLParserResult);
            }

            if (CollectionUtils.isNotEmpty(controlledObjects)) {
                webSQLParserResult.setStatus(SqlExecuteStatus.ACCESS_RESTRICTED.getValue());
                webSQLParserResult.setMessage(getFinalMessage(controlledObjects));
                return false;
            }

        } catch (Exception e) {
            log.error("访问管控，解析异常：", e);
            webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
            webSQLParserResult.setMessage("访问管控-解析异常! " + e.getMessage());
            return false;
        }

        return true;
    }

    public static String getFinalMessage(Set<ControlledObject> controlledObjects) {
        Set<String> box = new LinkedHashSet<>();
        for (ControlledObject controlledObject : controlledObjects) {
            box.add(MessageTemplate.of(controlledObject.getType(), controlledObject.getUniqueToken(), controlledObject.getControlledTime()));
        }
        return String.join("，且", box);
    }

    public static class MessageTemplate {
        static final String TOKEN1 = "当前操作的目标";
        static final String TOKEN3 = "(";
        static final String TOKEN5 = ")在管控时间内，管控时间为";

        public static String of(String type, String uniqueToken, String controlledTime) {
            return TOKEN1 + type + TOKEN3 + uniqueToken + TOKEN5 + controlledTime;
        }
    }

    public AccessibleTimeResult getAllAccessibleTimes(List<SqlAuthModel> sqlAuthModelList, String connectId) {

        AccessibleTimeResult result = new AccessibleTimeResult();

        try {

            if (sqlAuthModelList.isEmpty()) {
                return result;
            }

            Set<String> schemaIds = new HashSet<>();
            Set<String> tableNames = new HashSet<>();

            for (SqlAuthModel sqlAuthModel : sqlAuthModelList) {
                schemaIds.add(sqlAuthModel.getSchemaUniqueKey());
                tableNames.add(sqlAuthModel.getFullName());
            }

            List<AccessibleTime> instance = accessibleTimeMapper.getInstanceAccessibleTimes(connectId);
            result.setInstanceResult(instance);
            List<AccessibleTime> schema = accessibleTimeMapper.getSchemaAccessibleTimes(connectId, schemaIds);
            result.setSchemaResult(schema);
            List<AccessibleTime> table = accessibleTimeMapper.getTableAccessibleTimes(connectId, schemaIds, tableNames);
            result.setTableResult(table);

        } catch (Exception e) {
            log.error("get accessible times error!", e);
        }

        return result;
    }

    public Set<ControlledObject> getControlledObjects(List<AccessControlModel> accessControlModels, ParserParamDto paramDTO, String target, WebSQLParserResult webSQLParserResult) {

        final Set<ControlledObject> result = new LinkedHashSet<>();
        final List<String> permissionsDictionary = filterPermissionsDictionaryByTarget(webSQLParserInfo.getPermissionsDictionary(), target); // 可被特例放行的操作
        final Set<String> uncontrolledObjectTokens = new HashSet<>(); //已被解控的对象操作名单
        final String DOT_ = ".";
        final Set<ControlledObject> exportControlledObjects = new HashSet<>();

        try {
            outer:
            for (AccessControlModel accessControlModel : accessControlModels) {
                String operation = accessControlModel.getOperation();
                String ddlSubdivideOperation = accessControlModel.getDdlSubdivideOperation();
                String currentObjectOperationToken = target + DOT_ + accessControlModel.getName() + DOT_ + operation;

                secondOuter:
                for (AccessibleRule rule : accessControlModel.getAccessibleRules()) {
                    Calendar calendar = Calendar.getInstance(Locale.CHINA);
                    int len = rule.getAccessibleTime().length();
                    // [{}] 目前数组中只有一个对象
                    String substring = rule.getAccessibleTime().substring(1, len - 1);

                    SchemaAccessibleTime schemaAccessibleTime = JSON.parseObject(substring, SchemaAccessibleTime.class);

                    Map<String, List<String>> operations = schemaAccessibleTime.getOperation() instanceof Map ? (Map<String, List<String>>) schemaAccessibleTime.getOperation() : new HashMap<>();
                    String type = schemaAccessibleTime.getType();
                    Object part = schemaAccessibleTime.getPart();
                    List<Map<String, Object>> section = schemaAccessibleTime.getSection();

                    //TODO 当前IP是否不受限。尝试放行。换成成员变量userIp即可。
                    try {
                        if (!ipInRange(this.userIp, rule.getIp(), rule.getIp().contains("/"))) {
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("get user ipAddress error!", e);
                        continue;
                    }


                    // 当前日期是否在操作范围日期内
                    if (StringUtils.isNotBlank(type)) {

                        // 星期，月
                        if ("week".equalsIgnoreCase(type) || "month".equalsIgnoreCase(type)) {

                            List<Integer> controlledDays = new ArrayList<>();
                            if (part instanceof List) {
                                controlledDays = (List<Integer>) part;
                            }

                            LocalDate today = LocalDate.now();
                            if ("week".equalsIgnoreCase(type)) {
                                // 获取今天是这周的第几天
                                int dayOfWeek = today.getDayOfWeek().getValue();
                                if (!controlledDays.contains(dayOfWeek)) {
                                    continue;
                                }
                            } else if ("month".equalsIgnoreCase(type)) {
                                // 获取今天是这月的第几天
                                int dayOfMonth = today.getDayOfMonth();
                                if (!controlledDays.contains(dayOfMonth)) {
                                    continue;
                                }
                            }

                            // 策略
                        } else if ("strategy".equalsIgnoreCase(type)) {

                            if (part instanceof List) {
                                List<String> redisKeyList = (List<String>) part;
                                // redis中存放着受限策略的 具体日期
                                String redisStrategy = redisService.get("strategy");
                                if (redisStrategy == null || redisStrategy.isEmpty()) {
                                    continue;
                                }

                                Map<String, Object> redisStrategyMap = (Map<String, Object>) JSON.parseObject(redisStrategy, Map.class);
                                Object redisStrategyObject = redisStrategyMap.get("strategy");
                                if (redisStrategyObject == null || redisStrategyObject.toString() == null || redisStrategyObject.toString().isEmpty()) {
                                    continue;
                                }

                                redisStrategyMap = redisStrategyObject instanceof Map ? (Map<String, Object>) redisStrategyObject : new HashMap<>();
                                redisStrategyObject = redisStrategyMap.get(redisKeyList.get(0));
                                if (redisStrategyObject == null || redisStrategyObject.toString() == null || redisStrategyObject.toString().isEmpty()) {
                                    continue;
                                }

                                redisStrategyMap = redisStrategyObject instanceof Map ? (Map<String, Object>) redisStrategyObject : new HashMap<>();
                                if (redisStrategyMap.get("dates") == null) {
                                    continue; // 说明为空
                                }

                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                String dateStr = sdf.format(calendar.getTime());

                                Object dates = redisStrategyMap.get("dates");

                                if (dates instanceof String) {
                                    if (!((String) dates).contains(dateStr)) {
                                        continue;
                                    }
                                } else if (dates instanceof Collection) {
                                    if (!((Collection<?>) dates).contains(dateStr)) {
                                        continue;
                                    }
                                }
                            }

                        }
                    }

                    // 当前时间是否在操作范围日期内
                    if (CollectionUtils.isEmpty(section)) {
                        continue;
                    }

                    LocalDateTime requestDateTime;
                    if (paramDTO.getRequestTime() != null) {
                        requestDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(paramDTO.getRequestTime()), ZoneId.systemDefault());// 使用sql在窗口点击执行的时间作为当前需要验证的时间
                    } else {
                        requestDateTime = LocalDateTime.now();
                    }

                    LocalDate requestDate = requestDateTime.toLocalDate();

                    DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

                    Set<String> timesBox = new LinkedHashSet<>();
                    AtomicBoolean inOneOfTimes = new AtomicBoolean(false);
                    section.forEach(timeMap -> {
                        if (timeMap.get("begin") != null && timeMap.get("end") != null) {
                            String beginTime = (String) timeMap.get("begin");
                            String endTime = (String) timeMap.get("end");
                            timesBox.add(beginTime + "~" + endTime);
                            LocalDateTime beginLocalDateTime = LocalTime.parse(beginTime, timeFormatter).atDate(requestDate);
                            LocalDateTime endLocalDateTime = LocalTime.parse(endTime, timeFormatter).atDate(requestDate);
                            if (requestDateTime.isAfter(beginLocalDateTime) && requestDateTime.isBefore(endLocalDateTime)) {
                                inOneOfTimes.set(true);
                            }
                        }
                    });
                    if (!inOneOfTimes.get()) {
                        continue;
                    }
                    String limitTime = String.join("、", timesBox);

                    if ((DCConstants.DC_SCHEMA.equals(target) || DCConstants.DC_TABLE.equals(target)) &&
                            CommonUtil.isInstanceLevelOperation(operation, ddlSubdivideOperation)) {
                        continue;
                    }

                    // 当前操作是否不受限
                    if (!operations.isEmpty()) {

                        // 验证通过,则说明是特例放行的operation
                        boolean passOperation = CommonUtil.specialCasePass(operations, operation, permissionsDictionary);
                        // 权限细分验证
                        if (!passOperation && StringUtils.isNotBlank(ddlSubdivideOperation)) {
                            passOperation = CommonUtil.specialCasePass(operations, ddlSubdivideOperation, permissionsDictionary);
                        }
                        // COMMENT 按照ALTER、CREATE进行放行
                        if (!passOperation && SqlConstant.KEY_COMMENT.equalsIgnoreCase(operation)) {
                            List<String> authOperations = Arrays.asList(SqlConstant.KEY_ALTER, SqlConstant.KEY_CREATE, "ALTER_TABLE", "CREATE_TABLE");
                            for (String authOperation : authOperations) {
                                passOperation = CommonUtil.specialCasePass(operations, authOperation, permissionsDictionary);
                                if (passOperation) {
                                    break outer;
                                }
                            }
                        }

                        if (passOperation) {
                            uncontrolledObjectTokens.add(currentObjectOperationToken);
                            result.removeIf(c -> c.getType().equals(target) && c.getUniqueToken().equals(accessControlModel.getName()) && c.getOperation().equalsIgnoreCase(operation));
                        } else {
                            if (!uncontrolledObjectTokens.contains(currentObjectOperationToken)) {
                                result.add(new ControlledObject(target, accessControlModel.getName(), operation, limitTime));
                            }
                        }
                    } else {
                        //没有特例放行任何操作
                        if (!uncontrolledObjectTokens.contains(currentObjectOperationToken)) {
                            result.add(new ControlledObject(target, accessControlModel.getName(), operation, limitTime));
                        }
                    }

                    if (!operation.equalsIgnoreCase(SqlConstant.KEY_EXPORT)) {
                        handleExportControlledObjects(operations, permissionsDictionary, uncontrolledObjectTokens, target, accessControlModel.getName(), limitTime, exportControlledObjects);
                    }

                }

            }

            webSQLParserResult.setAccessControlCanExport(exportControlledObjects.isEmpty());

        } catch (Exception e) {
            log.error("get access control message error!", e);
        }

        return result;
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class ControlledObject {
        final String type;
        final String uniqueToken;
        final String operation;
        @EqualsAndHashCode.Exclude
        final String controlledTime;
    }

    protected boolean ipInRange(String requestIp, String ipNotation, boolean isCIDR) {
        if (requestIp == null) {
            if (ANY_IPADDRESS_CIDR.equals(ipNotation)) {
                return true;
            } else {
                log.warn("userIp is null !!!");
                return false;
            }
        }
        if (ipNotation == null) {
            log.warn("管控规则中的ip为null !!!");
            return false;
        }

        try {
            if (isCIDR) {
                SubnetUtils subnetUtils = new SubnetUtils(ipNotation);
                SubnetUtils.SubnetInfo info = subnetUtils.getInfo();
                if (!info.getCidrSignature().equals(ipNotation)) {
                    return false;
                }
                return info.isInRange(requestIp);
            }
            return requestIp.equals(ipNotation);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    protected List<String> filterPermissionsDictionaryByTarget(List<String> allPermissionsDictionary, String target) {
        if (DCConstants.DC_INSTANCE.equals(target)) {
            return allPermissionsDictionary;
        }
        if (DCConstants.DC_SCHEMA.equals(target) || DCConstants.DC_TABLE.equals(target)) {
            return allPermissionsDictionary.stream().filter(p -> !SqlConstant.KEY_GRANT.equalsIgnoreCase(p) && !SqlConstant.KEY_REVOKE.equalsIgnoreCase(p)).collect(Collectors.toList());
        }
        return allPermissionsDictionary;
    }

    protected boolean proveIsTable(SqlActionModel sqlActionModel, SqlAuthModel sqlAuthModel) {
        if (!sqlAuthModel.isTable()) {
            return false;
        }
        return KEYS_FOR_TABLE.contains(sqlAuthModel.getOperation().toUpperCase(Locale.ROOT)) || sqlActionModel.isSelectInto() || sqlActionModel.isMerge();
    }

    protected static final Set<String> KEYS_FOR_TABLE = Set.of(SqlConstant.KEY_SELECT, SqlConstant.KEY_INSERT, SqlConstant.KEY_LOAD, SqlConstant.STATISTICS,
                                                        SqlConstant.KEY_REPLACE, SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_DROP,
                                                        SqlConstant.KEY_TRUNCATE, SqlConstant.KEY_EXPORT, SqlConstant.KEY_IMPORT, SqlConstant.KEY_DESC);


    @Data
    public static class AccessibleTimeResult {
        private List<AccessibleTime> tableResult;
        private List<AccessibleTime> schemaResult;
        private List<AccessibleTime> instanceResult;
    }

    protected void handleExportControlledObjects(Map<String, List<String>> passOperationsMap, List<String> permissionsDictionary, Set<String> uncontrolledObjectTokens,
                                                 String targetType, String uniqueToken, String limitTime, Set<ControlledObject> exportControlledObjects) {
        final String operation = SqlConstant.KEY_EXPORT;
        final String currentObjectOperationToken = targetType + "." + uniqueToken + "." + operation;
        if (!passOperationsMap.isEmpty()) {
            boolean passOperation = CommonUtil.specialCasePass(passOperationsMap, operation, permissionsDictionary);
            if (passOperation) {
                uncontrolledObjectTokens.add(currentObjectOperationToken);
                exportControlledObjects.removeIf(c -> c.getType().equals(targetType) && c.getUniqueToken().equals(uniqueToken) && c.getOperation().equalsIgnoreCase(operation));
            } else {
                if (!uncontrolledObjectTokens.contains(currentObjectOperationToken)) {
                    exportControlledObjects.add(new ControlledObject(targetType, uniqueToken, operation, limitTime));
                }
            }
        } else {
            if (!uncontrolledObjectTokens.contains(currentObjectOperationToken)) {
                exportControlledObjects.add(new ControlledObject(targetType, uniqueToken, operation, limitTime));
            }
        }
    }

}
