
package com.dc.summer.model.impl.sql;

import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCQueryTransformer;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLUtils;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;

import java.util.regex.Pattern;

/**
 * Query transformer for LIMIT
 */
public class QueryTransformerLimit implements DBCQueryTransformer {

    //private static final Pattern SELECT_PATTERN = Pattern.compile("\\s*(?:select|update|delete|insert).+", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    public static final String KEYWORD_LIMIT = "LIMIT";
    public static final String KEYWORD_OFFSET = "OFFSET";

    public static final Pattern NON_LIMIT_QUERY_PATTERN = Pattern.compile("\\s+(LIMIT|OFFSET|INTO|UPDATE|PROCEDURE|FETCH)\\s+");

    private boolean supportsExtendedLimit;
    private boolean supportsOffsetKeyword;
    private Number offset;
    private Number length;
    private boolean limitSet;

    public QueryTransformerLimit() {
        this(true);
    }

    public QueryTransformerLimit(boolean supportsExtendedLimit) {
        this(supportsExtendedLimit, false);
    }

    public QueryTransformerLimit(boolean supportsExtendedLimit, boolean supportsOffsetKeyword) {
        this.supportsExtendedLimit = supportsExtendedLimit;
        this.supportsOffsetKeyword = supportsOffsetKeyword;
    }

    @Override
    public void setParameters(Object... parameters) {
        this.offset = (Number) parameters[0];
        this.length = (Number) parameters[1];
    }

    @Override
    public String transformQueryString(SQLQuery query) throws DBCException {
        String newQuery;
        String testQuery = query.getText().toUpperCase().trim();
        SQLDialect dialect = SQLUtils.getDialectFromDataSource(query.getDataSource());
        boolean plainSelect = query.isPlainSelect();
        if (!plainSelect && query.getType() == SQLQueryType.UNKNOWN) {
            // Not parsed. Try to check with simple matcher
            plainSelect = "SELECT".equals(SQLUtils.getFirstKeyword(dialect, testQuery));
        }
        if (plainSelect) {
            plainSelect = !NON_LIMIT_QUERY_PATTERN.matcher(testQuery).find();
        }
        if (!plainSelect) {
            // Do not use limit if it is not a select or it already has LIMIT or it is SELECT INTO statement
            Statement statement = query.getStatement();
            if (statement instanceof Select && (((Select) statement).getSelectBody() instanceof PlainSelect)) {
                PlainSelect selectBody = (PlainSelect) ((Select) statement).getSelectBody();
                if (selectBody.getIntoTables() == null && selectBody.getForUpdateTable() == null) {
                    Limit limit = new Limit();
                    if (supportsOffsetKeyword || selectBody.getOffset() != null) {
                        Offset newOffset = new Offset();
                       if (selectBody.getOffset() == null) {
                           newOffset.setOffset(new LongValue(offset.longValue()));
                       } else {
                           String oriOffset = selectBody.getOffset().getOffset().toString();
                           newOffset.setOffset(new LongValue(Long.parseLong(oriOffset) + offset.longValue()));
                       }
                       selectBody.setOffset(newOffset);
                       if (selectBody.getLimit() != null) {
                           limit = selectBody.getLimit();
                           limit.setRowCount(new LongValue(Long.parseLong(selectBody.getLimit().getRowCount().toString()) - offset.longValue()));
                       }
                    } else {
                        if (selectBody.getLimit() == null) {
                            limit.setOffset(new LongValue(offset.longValue()));
                        } else {
                            limit = selectBody.getLimit();
                            if (limit.getOffset() == null) {
                                limit.setOffset(new LongValue(offset.longValue()));
                            } else {
                                String oriOffset = limit.getOffset().toString();
                                limit.setOffset(new LongValue(Long.parseLong(oriOffset) + offset.longValue()));
                            }
                            limit.setRowCount(new LongValue(Long.parseLong(limit.getRowCount().toString()) - offset.longValue()));
                        }
                    }
                    if (limit.getRowCount() != null) {
                        long rowCount = Long.parseLong(limit.getRowCount().toString());
                        if (rowCount >  length.longValue()) {
                            limit.setRowCount(new LongValue(length.longValue()));
                        }
                    } else {
                        limit.setRowCount(new LongValue(length.longValue()));
                    }
                    selectBody.setLimit(limit);
                    newQuery = selectBody.toString();
                } else {
                    limitSet = false;
                    newQuery = query.getText();
                }
            } else {
                limitSet = false;
                newQuery = query.getText();
            }

        } else {
            if (supportsExtendedLimit) {
                newQuery = query.getText() + "\n" + KEYWORD_LIMIT + " " + offset + ", " + length;
            } else if (supportsOffsetKeyword) {
                // LIMIT + OFFSET
                newQuery = query.getText() + "\n" + KEYWORD_LIMIT + " " + length.longValue();
                if (offset.longValue() >= 0) {
                    newQuery += " " + KEYWORD_OFFSET + " " + offset.longValue();
                }
            } else {
                // We can limit only total row number
                newQuery = query.getText() + "\n" + KEYWORD_LIMIT + " " + (offset.longValue() + length.longValue());
            }
            limitSet = supportsExtendedLimit || supportsOffsetKeyword;
        }
        return newQuery;
    }

    @Override
    public void transformStatement(DBCStatement statement, int parameterIndex) throws DBCException {
        statement.setLimit(0, length.longValue() - 1);
    }
}
