package com.dc.parser.ext.oracle.check.rule.listener;

import com.dc.parser.ext.oracle.parser.autogen.OracleStatementBaseListener;
import com.dc.parser.ext.oracle.parser.autogen.OracleStatementParser;
import com.dc.parser.ext.oracle.utils.OracleHintParser;
import com.dc.parser.ext.oracle.utils.ParsedHint;
import lombok.Getter;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

@Getter
public class OracleCheckParalleListener extends OracleStatementBaseListener {

    /**
     * 是否超过并行度限制
     */
    public boolean isExceedParallelLimitCount = false;

    private int parallellimitCount;

    public OracleCheckParalleListener(int parallellimitCount) {
        this.parallellimitCount = parallellimitCount;
    }

    @Override
    public void enterHint(OracleStatementParser.HintContext ctx) {
        String hintComment = ctx.getText();

        List<ParsedHint> parsedHints = OracleHintParser.parse(hintComment);
        for (ParsedHint hint : parsedHints) {
            if ("parallel".equalsIgnoreCase(hint.getName())) {
                if (hint.getArguments().isEmpty()) {
                    isExceedParallelLimitCount = true;
                    break;
                }
                if (hint.getArguments().size() == 1) {
                    String arg = hint.getArguments().get(0);
                    if (!NumberUtils.isCreatable(arg) || Integer.parseInt(arg) > parallellimitCount) {
                        isExceedParallelLimitCount = true;
                        break;
                    }
                } else if (hint.getArguments().size() == 2) {
                    try {
                        int parallelCount = Integer.parseInt(hint.getArguments().get(1));
                        if (parallelCount > parallellimitCount) {
                            isExceedParallelLimitCount = true;
                            break;
                        }
                    } catch (NumberFormatException e) {
                        //第二个参数值可能是 DEFAULT、AUTO
                    }
                }
            }
        }
    }
}
