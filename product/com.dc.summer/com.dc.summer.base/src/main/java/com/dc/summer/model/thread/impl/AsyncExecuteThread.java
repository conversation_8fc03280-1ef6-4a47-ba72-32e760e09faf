package com.dc.summer.model.thread.impl;

import com.dc.springboot.core.model.thread.AbstractExecuteThread;
import com.dc.summer.model.type.ExecuteType;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;

@Service
public class AsyncExecuteThread extends AbstractExecuteThread<ExecuteType> {

    @Override
    public Collection<ExecuteType> getTypes() {
        return Set.of(ExecuteType.ASYNC_EXECUTE);
    }

    @Override
    public String getName() {
        return "async-execute";
    }
}
