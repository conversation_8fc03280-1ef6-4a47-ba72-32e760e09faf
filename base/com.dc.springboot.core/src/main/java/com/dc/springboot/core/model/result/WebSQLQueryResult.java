
package com.dc.springboot.core.model.result;

import com.dc.springboot.core.model.log.SensitiveAuthDetail;
import com.dc.springboot.core.model.log.SqlRecord;
import com.dc.springboot.core.model.parser.dto.ActionModelDto;
import com.dc.springboot.core.model.type.HighLightState;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.Log;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPErrorAssistant;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.type.DatabaseType;
import com.dc.type.SceneType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * Web SQL query results.
 */
@Data
@ApiModel(value = "Web SQL 查询结果")
public class WebSQLQueryResult {

    private static final Log log = Log.getLog(WebSQLQueryResult.class);

    @JsonIgnore
    private SqlRecord sqlRecord;

    @JsonIgnore
    private SqlHistory sqlHistory;

    @JsonIgnore
    private List<SensitiveAuthDetail> sensitiveAuthDetailList;

    @JsonIgnore
    private Boolean isSuccess;

    @ApiModelProperty(value = "结果集")
    private List<WebSQLQueryResultSet> resultSet;

    @ApiModelProperty(value = "展示 SQL")
    private String sql;

    @ApiModelProperty(value = "消息")
    private String message;

    @ApiModelProperty(value = "持续时间")
    private long duration;

    @ApiModelProperty(value = "fetchEnd时间")
    private long fetchEndTime;

    @ApiModelProperty(value = "访问频率")
    private String visitFrequency;

    @ApiModelProperty(value = "可以导出")
    private boolean canExport;

    @ApiModelProperty(value = "结果集只读 - true 不可编辑，false 可编辑")
    private boolean readOnly;

    /**
     * @see com.dc.springboot.core.model.type.SqlExecuteStatus
     */
    @ApiModelProperty(value = "SQL 执行状态")
    private int status;

    @ApiModelProperty(value = "SQL语句中的操作")
    private String operation;

    @ApiModelProperty(value = "行数限制")
    private long rowsLimit;

    @ApiModelProperty(value = "启用限制")
    private boolean isLimit;

    @ApiModelProperty(value = "无权限对象对应的信息")
    private List<Map<String, Object>> noPermissionObjects;

    @ApiModelProperty(value = "warnings 输出")
    private List<String> warnings;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "高亮状态")
    private int highLightState;

    @JsonIgnore
    private boolean executed;

    @ApiModelProperty(value = "更新行计数")
    private Long updateRowCount;

    @ApiModelProperty(value = "场景")
    private int scene;

    @ApiModelProperty(value = "可以生成SQL")
    private boolean canGenerateSql;

    @ApiModelProperty(value = "真实的schemaName")
    private String trueSchemaName;

    @ApiModelProperty(value = "错误位置集合")
    private DBPErrorAssistant.ErrorPosition[] errorPositions;

    @JsonIgnore
    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private HighLightInfo highLightInfo;

    @JsonIgnore
    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private SqlWarningsInfo sqlWarningsInfo;


    @ApiModelProperty(value = "操作列表")
    private List<ActionModelDto> actionList;

    @JsonIgnore
    private DatabaseType databaseType;

    @JsonIgnore
    private boolean hasNextSql;

    @JsonIgnore
    private boolean single;

    @ApiModelProperty(value = "可以查询总行数")
    private boolean canCountTotal;

    @ApiModelProperty(value = "备份消息")
    private String backupWarning;

    @ApiModelProperty(value = "结果集索引 - 用来导出数据")
    private int sqlIndex;

    @ApiModelProperty(value = "执行SQL下标 - 前端判断条数使用")
    private int execSqlIndex;

    @JsonIgnore
    @ApiModelProperty(value = "确定执行本条SQL - 默认是执行，特殊情况下不执行SQL，但是获取信息。")
    private boolean confirm = true;

    public void setResultSet(List<WebSQLQueryResultSet> resultSet) {
        if (!CollectionUtils.isEmpty(resultSet)) {
            for (int i = 0; i < resultSet.size(); i++) {
                resultSet.get(i).setIndex(i);
            }
        }
        this.resultSet = resultSet;
    }

    public Long getUpdateRowCount() {
        if (CollectionUtils.isEmpty(resultSet)) {
            return 0L;
        }
        if (updateRowCount == null) {
            updateRowCount = resultSet.stream()
                    .map(WebSQLQueryResultSet::getUpdateRowCount)
                    .filter(count -> count != null && count > 0)
                    .reduce(Long::sum)
                    .orElse(0L);
        }
        return updateRowCount;
    }

    private long getResultSetRowCount() {
        if (CollectionUtils.isEmpty(resultSet)) {
            return 0L;
        }
        return resultSet.stream()
                .map(WebSQLQueryResultSet::getRows)
                .filter(ArrayUtils::isNotEmpty)
                .map(rows -> Long.valueOf(rows.length))
                .reduce(Long::sum)
                .orElse(0L);
    }

    private long getExportRowsCount() {
        if (CollectionUtils.isEmpty(resultSet)) {
            return 0L;
        }
        return resultSet.stream()
                .map(WebSQLQueryResultSet::getExportRowsCount)
                .reduce(Long::sum)
                .orElse(0L);
    }

    public String getMessage() {
        if (message == null && status == SqlExecuteStatus.SUCCESS.getValue()) {
            SQLQueryType sqlQueryType = SQLQueryType.of(operation);
            if (sqlQueryType.isDML() || (sqlQueryType == SQLQueryType.CREATE && databaseType == DatabaseType.REDIS)) {
                message = formatUpdateMessage(getUpdateRowCount(), duration, fetchEndTime);
            } else if (!CollectionUtils.isEmpty(getResultSet()) && getResultSet().get(0).getColumns() != null) {
                message = formatSelectMessage(getExportRowsCount() > 0 ? getExportRowsCount() : getResultSetRowCount(), duration, fetchEndTime);
            } else {
                message = formatDefaultMessage(duration, fetchEndTime);
            }
        }
        return message;
    }

    private String formatUpdateMessage(Long count, long duration, long fetchEndTime) {
        return String.format("影响行数：[%d]行，耗时：[%s]ms，执行耗时：[%s]ms", count, duration, duration - fetchEndTime);
    }

    private String formatSelectMessage(Long count, long duration, long fetchEndTime) {
        return String.format("执行成功，当前返回：[%d]行，耗时：[%s]ms，执行耗时：[%s]ms", count, duration, duration - fetchEndTime);
    }

    private String formatDefaultMessage(long duration, long fetchEndTime) {
        return String.format("执行成功，耗时：[%s]ms，执行耗时：[%s]ms", duration, duration - fetchEndTime);
    }

    public void setStatus(int status) {
        this.status = status;
        if (isSuccess == null) {
            isSuccess = SqlExecuteStatus.of(status).hasPermission();
        }
    }

    public void setSqlRecord(SqlRecord sqlRecord) {
        if (sqlRecord != null) {
            sqlRecord.setMessage(getMessage());
            sqlRecord.setRecordStatus(status);
            sqlRecord.setIsSuccess(isSuccess);
        }
        this.sqlRecord = sqlRecord;
    }

    public HighLightInfo getHighLightInfo(DBRProgressMonitor monitor,
                                          String operation,
                                          DBPDataSource dataSource,
                                          boolean isAutoCommit,
                                          HighLightState highLightState,
                                          boolean needHighLight,
                                          boolean containsDblink) {
        if (highLightInfo == null) {
            if (needHighLight) {
                highLightInfo = new HighLightEffective(this, monitor, operation, dataSource, isAutoCommit, highLightState, containsDblink);
            } else {
                highLightInfo = new HighLightInvalid();
            }
        }
        return highLightInfo;
    }

    public SqlWarningsInfo getSqlWarningsInfo(DBRProgressMonitor monitor) {
        if (sqlWarningsInfo == null) {
            sqlWarningsInfo = new SqlWarningsInfo(monitor, this);
        }
        return sqlWarningsInfo;
    }

    public boolean isReadOnly() {
        if (!CollectionUtils.isEmpty(resultSet)) {
            if (resultSet.size() > 1) {
                readOnly = true;
            }
            // 存在一个结果集，可编辑或无主键，列不为空的情况
            // 判断列中一个主键都没有，就不可编辑。但凡存在一个主键，可可编辑。
            else if (resultSet.size() == 1 && (!readOnly || scene == SceneType.NO_PRIMARY_KEY.getValue()) && resultSet.get(0).getColumns() != null) {
                readOnly = Arrays.stream(resultSet.get(0).getColumns()).noneMatch(WebSQLQueryResultColumn::isPrimaryKey);
            }
        }
        return readOnly;
    }

    public void addWarnings(List<String> warnings) {
        if (warnings == null) {
            return;
        }
        if (this.warnings == null) {
            this.warnings = new ArrayList<>();
        }
        this.warnings = warnings;
    }
}
