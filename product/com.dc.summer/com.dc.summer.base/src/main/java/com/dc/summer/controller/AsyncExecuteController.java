package com.dc.summer.controller;

import com.dc.springboot.core.model.data.Result;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.AsyncExecuteExportMessage;
import com.dc.summer.model.data.message.AsyncExecuteMessage;
import com.dc.summer.model.data.message.AsyncInterruptMessage;
import com.dc.summer.model.data.result.AsyncExecuteExportResult;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.AsyncExecuteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.validation.Valid;


@Api("异步执行")
@Slf4j
@RequestMapping("/async-execute")
public class AsyncExecuteController {

    @Resource
    private AsyncExecuteService asyncExecuteService;

    @Resource
    private SummerThreadScheduler scheduler;

    @ApiOperation("异步执行")
    @PostMapping("/execute")
    public Result<WebAsyncTaskInfo> asyncExecute(@Valid @RequestBody AsyncExecuteMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = asyncExecuteService.asyncExecuteTask(message);
        scheduler.exec(ExecuteType.ASYNC_EXECUTE, webAsyncTaskInfo);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("异步执行导出")
    @PostMapping("/export")
    public Result<AsyncExecuteExportResult> asyncExport(@Valid @RequestBody AsyncExecuteExportMessage message) {
        asyncExecuteService.asyncExecuteExport(message);
        return Result.success();
    }

}
