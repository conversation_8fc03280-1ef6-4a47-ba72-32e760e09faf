package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.model.*;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.constants.ConnectAttributeConstants;
import com.dc.summer.constants.UCmdbConstants;
import com.dc.summer.model.*;
import com.dc.summer.service.DbConnectionService;
import com.dc.summer.service.FetchDatabaseEntityService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.utils.DateUtil;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.pingan.cdsf.driver.bridger.dto.PaDatabaseEntityDto;
import com.pingan.cdsf.driver.bridger.dto.PaInstanceDto;
import com.pingan.cdsf.driver.bridger.dto.PaSubInstanceDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("DatabaseEntityService")
public abstract class DatabaseEntityService implements FetchDatabaseEntityService {

    protected static final Gson gson = new Gson();

    @Resource
    public SummerMapper summerMapper;

    @Resource
    public TestConnectionTask testConnectionService;

    @Resource
    public DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    public DbConnectionService dbConnectionService;

    @Resource
    PaArchiveDataConsumerService paArchiveDataService;


    public List syncInstance(SyncInstanceDto syncInstanceDto) {

        //一对多(一个实例多个连接账号) ，每解析出一个InstanceUuid(平安实例) 定制化配置上多个连接账号(例如只读,只写),则返回多个DatabaseConnection
        List<PaInstanceObject> paInstanceObjectList = this.getInstanceData(syncInstanceDto);

        Integer saveCount = 0;
        Integer updateCount = 0;

        if (ObjectUtils.isEmpty(paInstanceObjectList)) {
            log.info("ENTITY-MESSAGE:110");
            return Arrays.asList(saveCount, updateCount);
        }

        //----------------离线数据更新:DbEntity,PaInstance,PaSubInstance,PaDatabaseEntity---------------start
        InstanceArchiveDto archive = new InstanceArchiveDto();
        archive.setPaInstanceObjectList(paInstanceObjectList);
        archive.setDbType(syncInstanceDto.getDbType());
        archive.setPaDatabaseEntityList(syncInstanceDto.getPaDatabaseEntityList());
        paArchiveDataService.doHandler(archive);
        //----------------离线数据更新:DbEntity,PaInstance,PaSubInstance,PaDatabaseEntity-----------------end


        //一对多(一个实例多个连接账号) key ： instance+username
        Map<String, DatabaseConnection> instanceUuidDataMap = null;
        // 查询已经存在的，
        List<String> paInstanceUuidList = paInstanceObjectList.stream().map(PaInstanceObject::getDatabaseConnection).map(DatabaseConnection::getSync).distinct().collect(Collectors.toList());
        List<DatabaseConnection> databaseConnectionData = databaseConnectionMapper.selectList(new QueryWrapper<DatabaseConnection>().lambda()
                .in(DatabaseConnection::getSync, paInstanceUuidList)
                .eq(DatabaseConnection::getIs_delete, 0)
        );

        //--
        if (null != databaseConnectionData && databaseConnectionData.size() > 0) {
            instanceUuidDataMap = databaseConnectionData.stream().collect(Collectors.toMap(a -> a.getSync() + "-" + a.getUsername(), c -> c, (k1, k2) -> k1));
        }

        //一对多(一个实例多个连接账号) ，每解析出一个InstanceUuid(平安实例) 定制化配置上多个连接账号(例如只读,只写),则返回多个DatabaseConnection
        for (PaInstanceObject instanceObject : paInstanceObjectList) {

            //从接口数据中构建的临时DatabaseConnection
            DatabaseConnection connection = instanceObject.getDatabaseConnection();

            String paInstanceUuid = connection.getSync();
            String paAccount = connection.getUsername();
            //一个平安实例+连接账号
            String key = paInstanceUuid + "-" + paAccount;

            //业务用户
            if (null != syncInstanceDto.getBusinessUserIdList() && syncInstanceDto.getBusinessUserIdList().size() > 0) {
                instanceObject.setBusinessUserIdList(syncInstanceDto.getBusinessUserIdList());
            }

            //接口中解析出DatabaseConnection对象扩展属性
            Map<String, ConnectExtendMap> extendListMap = null;
            String attributesListStr = connection.getExtended_attributes();
            if (StringUtils.isNotEmpty(attributesListStr)) {
                List<ConnectExtendMap> attributesList = gson.fromJson(attributesListStr, new TypeToken<List<ConnectExtendMap>>() {
                }.getType());
                extendListMap = attributesList.stream().collect(Collectors.toMap(ConnectExtendMap::getKey, t -> t, (k1, k2) -> k1));
            }

            //应用接口人
            if (null != extendListMap && extendListMap.containsKey(ConnectAttributeConstants.SERVICE_USER)) {
                ConnectExtendMap serviceUserExtend = extendListMap.get(ConnectAttributeConstants.SERVICE_USER);
                if (null != serviceUserExtend && StringUtils.isNotEmpty(serviceUserExtend.getValue())) {
                    //队列消费数据故能保避免重复提交
                    String serviceUserUuids = dbConnectionService.saveUserIfNotExists(serviceUserExtend.getValue());
                    instanceObject.setServiceUser(serviceUserUuids);
                }
            }

            if (null != instanceUuidDataMap && instanceUuidDataMap.containsKey(key)) {
                try {
                    boolean isChanged = false;

                    DatabaseConnection dbConnection = instanceUuidDataMap.get(key);
                    //数据库中保存的属性
                    String connectAttributesDataStr = dbConnection.getExtended_attributes();

                    if (StringUtils.isNotEmpty(connectAttributesDataStr)) {

                        List<ConnectExtendMap> connectAttributesList = gson.fromJson(connectAttributesDataStr, new TypeToken<List<ConnectExtendMap>>() {
                        }.getType());

                        for (ConnectExtendMap connectAttribute : connectAttributesList) {
                            //目前只更新manager_da, back_da, service_user
                            if (!Arrays.asList(ConnectAttributeConstants.MANAGER_DA, ConnectAttributeConstants.BACK_DA, ConnectAttributeConstants.SERVICE_USER).contains(connectAttribute.getKey())) {
                                continue;
                            }

                            //例如：目前环境端口换了，管理员先通过手动修改端口验证连接。避免UCMDB管理人员没有实时同步，导致次日又刷成错误的数据
                            if (null != extendListMap && extendListMap.containsKey(connectAttribute.getKey())) {
                                ConnectExtendMap connectExtendMap = extendListMap.get(connectAttribute.getKey());
                                if (null != connectAttribute.getValue() && !connectAttribute.getValue().equals(connectExtendMap.getValue())) {
                                    connectAttribute.setValue(connectExtendMap.getValue());
                                    isChanged = true;
                                }
                            }
                        }

                        if (isChanged) {
                            String extendAttr = gson.toJson(connectAttributesList);
                            dbConnection.setGmt_modified(DateUtil.date2Str(new Date(), DateUtil.ymd_hms_str_1));
                            dbConnection.setExtended_attributes(extendAttr);  //仅更新属性
                        }
                    }

                    if (isChanged) {
                        List<String> businessUserIdList = syncInstanceDto.getBusinessUserIdList();
                        String serviceUser = instanceObject.getServiceUser();
                        String uniqueKey = dbConnection.getUnique_key();
                        databaseConnectionMapper.updateById(dbConnection);
                        dbConnectionService.updateInstanceUser(Arrays.asList(uniqueKey), syncInstanceDto.getCreateUser().getUniqueKey(), businessUserIdList, serviceUser);
                        dbConnectionService.updateGroup(Arrays.asList(uniqueKey), syncInstanceDto.getCreateUser().getUniqueKey(), businessUserIdList);
                        updateCount++;
                    }

                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                    log.error("UPDATE-INSTANCE:401");
                }
            } else {
                try {

                    //手动添加CMDB资产sync字段为空，避免重复新增，按ip+port+账号(一个实例多个账号) 在判断一次
                    DatabaseConnection paInstance = instanceObject.getDatabaseConnection();
                    long startTime = System.currentTimeMillis();
                    List<DatabaseConnection> exists = databaseConnectionMapper.selectList(new QueryWrapper<DatabaseConnection>()
                            .eq(true, "db_type", paInstance.getDb_type())
                            .eq(true, "ip", paInstance.getIp())
                            .eq(true, "port", paInstance.getPort())
                            .eq(StringUtils.isNotBlank(paInstance.getService_name()), "service_name", paInstance.getService_name())
                            .eq(true, "username", paInstance.getUsername())  //账号
                            .eq(true, "is_delete", 0)
                            .isNull(true, "sync")
                            .last("limit 2"));
                    long endTime = System.currentTimeMillis();
                    log.warn(String.format("%s，耗时：[%s]ms.", "查询是否存在手动添加CMDB资产", (endTime - startTime)));

                    if (null != exists && exists.size() > 1) {
                        log.warn("平安实例信息匹配多条记录，请联系管理员");
                        log.warn("paInstance,::{}", gson.toJson(paInstance));
                        log.warn("existList,::{}", gson.toJson(exists));
                        continue;
                    }

                    if (null != exists && exists.size() == 1) {
                        log.warn("平安实例已手动创建::{}, 绑定实例UUID::{}", gson.toJson(paInstance), paInstance.getSync());
                        DatabaseConnection databaseConnection = exists.stream().findFirst().get();
                        //手动创建的实例sync 一定为空
                        if (StringUtils.isEmpty(databaseConnection.getSync())) {
                            //更新实例UUID 到sync
                            databaseConnection.setSync(paInstance.getSync());
                            //  其他属性覆盖
                            databaseConnection.setExtended_attributes(paInstance.getExtended_attributes());
                            databaseConnectionMapper.updateById(databaseConnection);
                            updateCount++;
                        }
                        continue;
                    }

                    //测试连接交予线程处理，因为每个实例情况不一，如：验证超时,密码过期等...
                    testConnectionService.submitTask(instanceObject);
                    saveCount++;
                } catch (InterruptedException e) {
                    log.error("SUBMIT-TASK:400");
                    e.printStackTrace();
                }
            }
        }
        return Arrays.asList(saveCount, updateCount);
    }

    @Override
    public String testConnection(PaInstanceObject instance) {
        DatabaseConnection databaseConnection = instance.getDatabaseConnection();
        TestConnectionDto testConnectionDto = instance.getTestConnectionDto();
        try {
            List driverPList = new ArrayList();
            driverPList.add(testConnectionDto.getTimeParams());
            databaseConnection.setDriver_properties(gson.toJson(driverPList));
            DatabaseConnectionDto databaseConnectionDto = summerMapper.toDatabaseConnectionDto(databaseConnection);
            ConnectionConfig connectionConfig = databaseConnectionDto.buildConnectionConfig(null, null);
            TestConnectionMessage testConnectionMessage = summerMapper.toTestConnectionMessage(connectionConfig);
            String driverId = WebSQLContextInfo.test(testConnectionMessage);
            return driverId;
        } catch (Exception e) {
            log.error("{},测试连接失败：{}", databaseConnection.getConnection_desc(), e.getMessage());
        }
        return null;
    }


    public abstract Integer getDbType();

    public abstract String getConnect(JsonObject message);

    public abstract String getConnectDesc(JsonObject message);

    public abstract List<PaInstanceObject> getInstanceData(SyncInstanceDto syncInstanceDto);

    protected String getRuleByCustomer(String customerName) {
        if (StringUtils.isEmpty(customerName)) {
            log.debug("Rule-MESSAGE:258");
            return null;
        }
        for (Map.Entry<String, List<String>> entry : UCmdbConstants.ruleList.entrySet()) {
            List<String> value = entry.getValue();
            if (value.contains(customerName)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public String getDictName(String key) {
        if (StringUtils.isEmpty(key)) {
            log.debug("DICT-NAME-MESSAGE:256");
            return null;
        }
        if (!UCmdbConstants.paMap.containsKey(key)) {
            log.debug("DICT-NAME-MESSAGE:257,    KEY:{}", key);
            return null;
        }
        return UCmdbConstants.paMap.get(key);
    }

    public Integer getEnvironment(String environment) {
        Integer environmentId = 4;
        if ("DBE:PRD".equals(environment)) {
            environmentId = 1;
        } else if ("DBE:DEV".equals(environment)) {
            environmentId = 2;
        } else if ("DBE:STG".equals(environment)) {
            environmentId = 3;
        }
        return environmentId;
    }

    public String getCategoryId(String customerNumber, Map<String, Category> categoryMap) {
        String categoryId = null;
        if (null != categoryMap && categoryMap.containsKey(customerNumber)) {
            Category category = categoryMap.get(customerNumber);
            categoryId = category.getUnique_key();
        }
        return categoryId;
    }

    public Integer getSecurityRuleSetId(Integer dbType, String ruleName, List<SecurityRuleSet> securityRuleSetList) {
        Integer securityRuleSetId = 0;
        if (null != securityRuleSetList && securityRuleSetList.size() > 0) {
            //String ruleName = this.getRuleByCustomer(customerName);
            for (SecurityRuleSet securityRuleSet : securityRuleSetList) {
                //实例根据 受益人找规则集， 如果找不到就选默认的
                if (dbType.equals(securityRuleSet.getDbType())) {
                    if (securityRuleSet.getIsBuiltIn().equals(1)) {
                        securityRuleSetId = securityRuleSet.getId();  //默认
                    }
                    if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName)) {
                        securityRuleSetId = securityRuleSet.getId();
                        break;
                    }
                }
            }
        }
        return securityRuleSetId;
    }

    public static PaDatabaseEntity convert(PaDatabaseEntityDto paDatabaseEntityDto, Date date) {
        PaDatabaseEntity paDatabaseEntity = new PaDatabaseEntity();
        paDatabaseEntity.setEntityName(paDatabaseEntityDto.getEntityName());
        paDatabaseEntity.setDbType(paDatabaseEntityDto.getDbType());
        paDatabaseEntity.setIsManaged(paDatabaseEntityDto.getIsManaged());
        paDatabaseEntity.setCustomerNumber(paDatabaseEntityDto.getCustomerNumber());
        paDatabaseEntity.setCustomerName(paDatabaseEntityDto.getCustomerName());
        paDatabaseEntity.setBu(paDatabaseEntityDto.getBu());
        paDatabaseEntity.setManagerDa(paDatabaseEntityDto.getManagerDa());
        paDatabaseEntity.setBackDa(paDatabaseEntityDto.getBackDa());
        paDatabaseEntity.setDatabaseVersion(paDatabaseEntityDto.getDatabaseVersion());
        paDatabaseEntity.setInfraType(paDatabaseEntityDto.getInfraType());
        paDatabaseEntity.setEntityUuid(paDatabaseEntityDto.getEntityUuid());
        paDatabaseEntity.setGmtCreate(date);
        paDatabaseEntity.setGmtModified(date);
        paDatabaseEntity.setIsDelete(0);
        return paDatabaseEntity;
    }

    public static PaInstance convert(PaInstanceDto paInstanceDTO, Date date) {
        PaInstance paInstance = new PaInstance();
        paInstance.setInstanceName(paInstanceDTO.getInstanceName());
        paInstance.setEntityUuid(paInstanceDTO.getEntityUuid());
        paInstance.setEnvironment(paInstanceDTO.getEnvironment());
        paInstance.setDomainName(paInstanceDTO.getDomainName());
        paInstance.setVip(paInstanceDTO.getVip());
        paInstance.setPort(paInstanceDTO.getPort());
        paInstance.setDefaultRole(paInstanceDTO.getDefaultRole());
        paInstance.setDatabaseVersion(paInstanceDTO.getDatabaseVersion());
        paInstance.setServiceUser(paInstanceDTO.getServiceUser());
        paInstance.setStatus(paInstanceDTO.getStatus());
        paInstance.setCyberarkEntityName(paInstanceDTO.getCyberarkEntityName());
        paInstance.setArchitectureType(paInstanceDTO.getArchitectureType());
        paInstance.setCreateMethod(paInstanceDTO.getCreateMethod());
        paInstance.setDeployEcology(paInstanceDTO.getDeployEcology());
        paInstance.setSystemName(paInstanceDTO.getSystemName());
        paInstance.setInstanceUuid(paInstanceDTO.getInstanceUuid());
        paInstance.setIsSeparate(paInstanceDTO.getIsSeparate());
        paInstance.setGmtCreate(date);
        paInstance.setGmtModified(date);
        paInstance.setIsDelete(0);
        return paInstance;
    }

    public static PaSubInstance convert(PaSubInstanceDto paSubInstanceDto, Date date) {
        PaSubInstance paSubInstance = new PaSubInstance();
        paSubInstance.setInstanceName(paSubInstanceDto.getInstanceName());
        paSubInstance.setDomainName(paSubInstanceDto.getDomainName());
        paSubInstance.setVip(paSubInstanceDto.getVip());
        paSubInstance.setDefaultRole(paSubInstanceDto.getDefaultRole());
        paSubInstance.setDatabaseVersion(paSubInstanceDto.getDatabaseVersion());
        paSubInstance.setHostIp(paSubInstanceDto.getHostIp());
        paSubInstance.setHostPort(paSubInstanceDto.getHostPort());
        paSubInstance.setStatus(paSubInstanceDto.getStatus());
        paSubInstance.setPort(paSubInstanceDto.getPort());
        paSubInstance.setSystemName(paSubInstanceDto.getSystemName());
        paSubInstance.setInstanceUuid(paSubInstanceDto.getInstanceUuid());
        paSubInstance.setParentInstanceId(paSubInstanceDto.getParentInstanceId());
        paSubInstance.setEntityUuid(paSubInstanceDto.getEntityUuid());
        paSubInstance.setGmtCreate(date);
        paSubInstance.setGmtModified(date);
        paSubInstance.setIsDelete(0);
        return paSubInstance;
    }

}
