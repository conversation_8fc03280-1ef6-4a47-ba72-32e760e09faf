package com.dc.summer.service.transfer;

import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.registry.center.Global;
import com.dc.repository.mysql.mapper.OrderDownloadFileMapper;
import com.dc.repository.mysql.model.OrderDownloadFile;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.message.AlertNotifyMessage;
import com.dc.utils.http.FilePurposeType;
import com.dc.utils.http.FileUtil;
import com.dc.summer.model.utils.ZipUtils;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Slf4j
public class WebDataTransferHelper {

    @Getter
    private Date exportDate = new Date();

    private boolean isSingle = true;

    private int serialNumber = 0;

    @Getter
    private Path dataExportFolder;

    private File folder;

    @Setter
    private Path exportFile;

    private boolean needToZipAndUpload;

    private boolean deleteResultInfo;

    private WebDataTransferName transferZipName;

    @Getter
    @Setter
    private WebDataTransferName transferFileName;

    private final Runnable deleteRunnable;

    private final String folderName;

    private final Map<String, File> fileNameMap = new HashMap<>();

    public WebDataTransferHelper(String folderName, Runnable deleteRunnable) {
        this.folderName = folderName;
        this.deleteRunnable = deleteRunnable;
    }

    public void reloading(
            boolean needToZipAndUpload,
            boolean isSingle,
            boolean deleteResultInfo,
            WebDataTransferName transferZipName,
            WebDataTransferName transferFileName) {
        this.needToZipAndUpload = needToZipAndUpload;
        this.isSingle = isSingle;
        this.deleteResultInfo = deleteResultInfo;
        this.transferZipName = transferZipName;
        this.transferFileName = transferFileName;
        this.dataExportFolder = Paths.get(Global.getEXPORT());
        this.folder = null;
        this.exportFile = null;

        if (!isSingle) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("导出睡眠失败！", e);
                Thread.currentThread().interrupt();
            }
        }

        if (deleteResultInfo) {
            dataExportFolder = dataExportFolder.resolve(folderName);
            dataExportFolder = getExportFile(this.transferZipName, dataExportFolder, -1, null, exportDate);
            folder = dataExportFolder.toFile();
        }

    }

    public void addExportFile(File file, boolean force) {
        if (force || !deleteResultInfo) {
            fileNameMap.put(file.getName(), file);
        }
    }

    public Path getExportFile(int size) {
        if (isSingle && !needToZipAndUpload) {
            isSingle = false;
        }
        int index = size == 1 && isSingle ? 0 : serialNumber + 1;
        this.exportFile = getExportFile(transferFileName, dataExportFolder, index, null, exportDate);
        if (!fileNameMap.containsKey(exportFile.toFile().getName())) {
            serialNumber++;
        }
        return this.exportFile;
    }

    public Path getCustomExportFile(int size) {
        boolean tempSingle = isSingle;
        if (isSingle && !needToZipAndUpload) {
            isSingle = false;
        }
        int index = size == 1 && isSingle ? 0 : serialNumber;
        this.exportFile = getExportFile(transferFileName, dataExportFolder, index, null, exportDate);
        isSingle = tempSingle;
        return this.exportFile;
    }

    public File toSingleExportFile(
            String extName, boolean needToZipAndUpload) {

        try {
            // 计算结果集被分割的情况
            ArrayList<File> tempFiles = new ArrayList<>();
            for (File file : fileNameMap.values()) {
                File[] listFiles = file.getParentFile().listFiles();
                assert listFiles != null;
                for (File otherFile : listFiles) {
                    String otherFileName = otherFile.getName();
                    if (otherFileName.startsWith(file.getName()) && otherFileName.endsWith(extName)) {
                        tempFiles.add(otherFile);
                    }
                }
            }
            boolean needZip = tempFiles.size() > 1 || serialNumber > 1;
            if (needToZipAndUpload && needZip) {
                exportFile = getExportFile(transferZipName, dataExportFolder, tempFiles.size(), "zip", exportDate);
                ZipUtils.createZip(exportFile.toUri().getPath(), tempFiles, folder, null);
            } else if (!needToZipAndUpload) {
                exportFile = getExportFile(transferFileName, dataExportFolder, 1, extName, exportDate);
            } else {
                exportFile = getExportFile(transferFileName, dataExportFolder, -1, extName, exportDate);
            }
        } catch (Exception e) {
            log.error("生成文件失败！", e);
            throw new RuntimeException(e);
        } finally {
            exportDate = new Date();
            isSingle = true;
            serialNumber = 0;
            fileNameMap.clear();
        }
        return exportFile.toFile();
    }

    public static String toUpload(
            String dcBackendPath,
            String uploadPathApi,
            File file,
            String userId) throws Exception {
        String uploadPath = null;
        try {
            uploadPath = FileUtil.fileUpload(dcBackendPath + uploadPathApi, file, false, userId, FilePurposeType.UPLOAD_RESULT_SET);
//            uploadPath = WebDataTransferHelper.restUpload(dcBackendPath + uploadPathApi, file, restTemplate);
        } catch (Exception e) {
            log.error("上传失败！", e);
            throw e;
        }
        return uploadPath;
    }


    public static String toZipAndUpload(
            String dcBackendPath,
            String encryptPassword,
            String uploadPathApi,
            ArrayList<File> files,
            String zipPath,
            OrderDownloadFileMapper downloadFileMapper,
            String fileName,
            Integer orderId,
            String downloadLimit,
            String userId) {

        String uploadPath = null;
        File zipFile = null;
        try {
            ZipUtils.createZip(zipPath, files, null, encryptPassword);
            zipFile = new File(zipPath);
            uploadPath = FileUtil.fileUpload(dcBackendPath + uploadPathApi, zipFile, false, userId, FilePurposeType.UPLOAD_RESULT_SET);
//            uploadPath = WebDataTransferHelper.restUpload(dcBackendPath + uploadPathApi, zipFile, restTemplate);

            OrderDownloadFile orderDownloadFile = new OrderDownloadFile();
            orderDownloadFile.setFileName(fileName);
            orderDownloadFile.setFilePath(uploadPath);
            orderDownloadFile.setRelationId(orderId);
            orderDownloadFile.setDownloadRule(downloadLimit);
            downloadFileMapper.saveDownloadFile(orderDownloadFile);

            BackendClient backendClient = Resource.getBean(BackendClient.class);
            SummerConfig summerConfig = Resource.getBean(SummerConfig.class);
            AlertNotifyMessage alertNotifyMessage = new AlertNotifyMessage().makeDcExportDataAlert(orderId, orderDownloadFile.getFileId());
            backendClient.saveAlertNotify(Client.getClient(summerConfig.getPath().getDcBackend()), alertNotifyMessage);
        } catch (Exception e) {
            log.error("上传失败！", e);
            throw new RuntimeException(e);
        } finally {
            files.clear();
            if (zipFile != null) {
                zipFile.delete();
                zipFile.getParentFile().delete();
            }
        }
        return uploadPath;
    }

    public void tryToZipAndUpload(
            boolean isSuccess,
            String dcBackendPath,
            String userId,
            String encryptPassword,
            WebSQLTransferResult transferResult,
            String extName,
            String uploadPathApi,
            OriginType originType) {

        try {
            if (!needToZipAndUpload || !isSuccess) {
                return;
            }
            boolean hasPwd = StringUtils.isNotBlank(encryptPassword);
            // 计算结果集被分割的情况
            ArrayList<File> tempFiles = new ArrayList<>();
            for (File file : fileNameMap.values()) {
                File[] listFiles = file.getParentFile().listFiles();
                assert listFiles != null;
                for (File otherFile : listFiles) {
                    String otherFileName = otherFile.getName();
                    if (otherFileName.startsWith(file.getName()) && otherFileName.endsWith(extName)) {
                        tempFiles.add(otherFile);
                    }
                }
            }
            if (transferZipName instanceof WebDataTransferName.ZipName) {
                File[] listFiles = dataExportFolder.toFile().listFiles();
                assert listFiles != null;
                tempFiles.addAll(List.of(listFiles));
            }
            boolean needZip = tempFiles.size() > 1 || serialNumber > 1;
            if (needZip || hasPwd) {
                exportFile = getExportFile(transferZipName, dataExportFolder, tempFiles.size(), "zip", exportDate);
                ZipUtils.createZip(exportFile.toUri().getPath(), tempFiles, folder, encryptPassword);
            } else {
                exportFile = getExportFile(transferFileName, dataExportFolder, -1, extName, exportDate);
            }

            if (exportFile != null) {
                String uploadPath = FileUtil.fileUpload(dcBackendPath + uploadPathApi, exportFile.toFile(), userId, FilePurposeType.UPLOAD_RESULT_SET);
                transferResult.setPath(uploadPath);
            }
            if (hasPwd) {
                BackendClient backendClient = Resource.getBean(BackendClient.class);
                SummerConfig summerConfig = Resource.getBean(SummerConfig.class);
                AlertNotifyMessage alertNotifyMessage = new AlertNotifyMessage().makeDcExportEncryAlert(userId, exportFile.toFile().getName(), encryptPassword, originType);
                backendClient.saveAlertNotify(Client.getClient(summerConfig.getPath().getDcBackend()), alertNotifyMessage);
            }
        } catch (Exception e) {
            log.error("上传失败！", e);
            transferResult.setMessage(e.getMessage());
            transferResult.setSuccess(false);
        } finally {
            exportDate = new Date();
            isSingle = true;
            serialNumber = 0;
            fileNameMap.clear();
            if (folder != null) {
                folder.delete();
                folder.getParentFile().delete();
            }
            if (this.deleteResultInfo && this.deleteRunnable != null) {
                this.deleteRunnable.run();
            }
        }
    }

    private Path getExportFile(WebDataTransferName transferName, Path dataExportFolder, int index, String extName, Date date) {
        String name = transferName.getName(index, extName, date);
        return dataExportFolder.resolve(name);
    }

}
